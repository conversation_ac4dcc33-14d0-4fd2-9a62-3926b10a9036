<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\SignupController;
use App\Http\Controllers\SurveyController;
use App\Http\Controllers\QuoteController;

Route::get('/dashboard', function () {
    $userdetails = session('userdetails');
    $usertype = session('usertype');
    return view('dashboard', compact('userdetails', 'usertype'));
});

Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [LoginController::class, 'login']);

Route::get('signup', [SignupController::class, 'showSignupForm'])->name('signup');
Route::post('signup', [SignupController::class, 'submitSignup'])->name('signup.submit');

Route::get('/survey/form', [SurveyController::class, 'showForm']);
Route::post('/survey/submit', [SurveyController::class, 'submit']);

Route::get('/survey', [SurveyController::class, 'index']);
Route::get('/survey/view/{id}', [SurveyController::class, 'view']);

Route::get('/', function () {
    return view('welcome');
});

// Quote routes
Route::get('/quotation', [QuoteController::class, 'index']);
Route::get('/quote/form/{customer_id?}', [QuoteController::class, 'showForm']);
Route::get('/quote/view/{id}', [QuoteController::class, 'view']);
Route::post('/quote/submit', [QuoteController::class, 'submit']);

// Support route
Route::get('/support', function () {
    $usertype = session('usertype');
    $userdetails = session('userdetails');
    return view('support', compact('usertype', 'userdetails'));
});
