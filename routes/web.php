<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\SignupController;
use App\Http\Controllers\SurveyController;
use App\Http\Controllers\QuoteController;

Route::get('/dashboard', function () {
    $userdetails = session('userdetails');
    $usertype = session('usertype');

    // Get dashboard statistics
    $totalSurveys = \App\Models\Customer::count();
    $totalQuotes = \App\Models\Quote::count();
    $pendingQuotes = \App\Models\Quote::where('status', 'draft')->count();
    $acceptedQuotes = \App\Models\Quote::where('status', 'accepted')->count();
    $recentSurveys = \App\Models\Customer::orderBy('created_at', 'desc')->limit(3)->get();
    $recentQuotes = \App\Models\Quote::with('customer')->orderBy('created_at', 'desc')->limit(3)->get();

    return view('dashboard', compact(
        'userdetails',
        'usertype',
        'totalSurveys',
        'totalQuotes',
        'pendingQuotes',
        'acceptedQuotes',
        'recentSurveys',
        'recentQuotes'
    ));
});

Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [LoginController::class, 'login']);

Route::get('signup', [SignupController::class, 'showSignupForm'])->name('signup');
Route::post('signup', [SignupController::class, 'submitSignup'])->name('signup.submit');

Route::get('/survey/form', [SurveyController::class, 'showForm']);
Route::post('/survey/submit', [SurveyController::class, 'submit']);

Route::get('/survey', [SurveyController::class, 'index']);
Route::get('/survey/view/{id}', [SurveyController::class, 'view']);

Route::get('/', function () {
    return view('welcome');
});

// Quote routes
Route::get('/quotation', [QuoteController::class, 'index']);
Route::get('/quote/form/{customer_id?}', [QuoteController::class, 'showForm']);
Route::get('/quote/view/{id}', [QuoteController::class, 'view']);
Route::post('/quote/submit', [QuoteController::class, 'submit']);

// Support route
Route::get('/support', function () {
    $usertype = session('usertype');
    $Userdetails = session('userdetails');
    return view('support', compact('usertype', 'Userdetails'));
});

// Logout route
Route::get('/logout', function () {
    session()->flush();
    return redirect('/login')->with('success', 'You have been logged out successfully.');
});
