<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('urjamitras', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('father_name');
            $table->integer('age');
            $table->string('mobile', 15);
            $table->string('email')->unique();
            $table->string('business_profile');
            $table->text('address');
            $table->string('aadhaar', 12)->nullable();
            $table->string('pan', 10)->nullable();
            $table->string('gstin', 15)->nullable();
            $table->string('aadhaar_doc')->nullable();
            $table->string('pan_doc')->nullable();
            $table->string('gst_doc')->nullable();
            $table->string('password');
            $table->rememberToken();
            $table->timestamps();
        });
    }
    public function down(): void
    {
        Schema::dropIfExists('urjamitras');
    }
};
