<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quotes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->string('quote_number')->unique();
            $table->decimal('system_capacity', 8, 2); // in KW
            $table->integer('panel_count');
            $table->string('panel_type');
            $table->decimal('panel_wattage', 8, 2);
            $table->string('inverter_type');
            $table->decimal('inverter_capacity', 8, 2);
            $table->string('mounting_structure');
            $table->decimal('system_cost', 10, 2);
            $table->decimal('installation_cost', 10, 2);
            $table->decimal('total_cost', 10, 2);
            $table->decimal('subsidy_amount', 10, 2)->nullable();
            $table->decimal('final_cost', 10, 2);
            $table->text('terms_conditions')->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', ['draft', 'sent', 'accepted', 'rejected'])->default('draft');
            $table->date('valid_until');
            $table->string('created_by_usertype');
            $table->unsignedBigInteger('created_by_userid');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quotes');
    }
};
