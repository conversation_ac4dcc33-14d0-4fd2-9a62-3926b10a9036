<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            // Check if columns don't exist before adding
            if (!Schema::hasColumn('quotes', 'system_type')) {
                $table->string('system_type')->nullable()->after('system_capacity');
            }
            if (!Schema::hasColumn('quotes', 'mounting_type')) {
                $table->string('mounting_type')->nullable()->after('mounting_structure');
            }
            if (!Schema::hasColumn('quotes', 'expert_rfi_mounting')) {
                $table->boolean('expert_rfi_mounting')->default(false)->after('mounting_type');
            }

            // Cost Breakdown - Components
            if (!Schema::hasColumn('quotes', 'inverter_cost')) {
                $table->decimal('inverter_cost', 10, 2)->nullable()->after('expert_rfi_mounting');
            }
            if (!Schema::hasColumn('quotes', 'mounting_structure_cost')) {
                $table->decimal('mounting_structure_cost', 10, 2)->nullable()->after('inverter_cost');
            }
            if (!Schema::hasColumn('quotes', 'transformers_labor_cost')) {
                $table->decimal('transformers_labor_cost', 10, 2)->nullable()->after('mounting_structure_cost');
            }
            if (!Schema::hasColumn('quotes', 'permits_documentation_cost')) {
                $table->decimal('permits_documentation_cost', 10, 2)->nullable()->after('transformers_labor_cost');
            }
            if (!Schema::hasColumn('quotes', 'gst_cost')) {
                $table->decimal('gst_cost', 10, 2)->nullable()->after('permits_documentation_cost');
            }

            // Financial Information
            if (!Schema::hasColumn('quotes', 'full_payment')) {
                $table->decimal('full_payment', 10, 2)->nullable()->after('final_cost');
            }
            if (!Schema::hasColumn('quotes', 'government_subsidy')) {
                $table->decimal('government_subsidy', 10, 2)->nullable()->after('full_payment');
            }
            if (!Schema::hasColumn('quotes', 'net_amount_after_subsidy')) {
                $table->decimal('net_amount_after_subsidy', 10, 2)->nullable()->after('government_subsidy');
            }
            if (!Schema::hasColumn('quotes', 'estimated_payback_period')) {
                $table->decimal('estimated_payback_period', 8, 2)->nullable()->after('net_amount_after_subsidy');
            }

            // Savings Information
            if (!Schema::hasColumn('quotes', 'monthly_energy_generation_kwh')) {
                $table->decimal('monthly_energy_generation_kwh', 10, 2)->nullable()->after('estimated_payback_period');
            }
            if (!Schema::hasColumn('quotes', 'monthly_savings')) {
                $table->decimal('monthly_savings', 10, 2)->nullable()->after('monthly_energy_generation_kwh');
            }
            if (!Schema::hasColumn('quotes', 'annual_savings')) {
                $table->decimal('annual_savings', 10, 2)->nullable()->after('monthly_savings');
            }
            if (!Schema::hasColumn('quotes', 'year_total_savings')) {
                $table->decimal('year_total_savings', 10, 2)->nullable()->after('annual_savings');
            }

            // Enhanced Terms & Conditions
            if (!Schema::hasColumn('quotes', 'warranty_details')) {
                $table->text('warranty_details')->nullable()->after('year_total_savings');
            }
            if (!Schema::hasColumn('quotes', 'installation_timeline')) {
                $table->text('installation_timeline')->nullable()->after('warranty_details');
            }
            if (!Schema::hasColumn('quotes', 'payment_terms_detailed')) {
                $table->text('payment_terms_detailed')->nullable()->after('installation_timeline');
            }
            if (!Schema::hasColumn('quotes', 'validity_details')) {
                $table->text('validity_details')->nullable()->after('payment_terms_detailed');
            }
            if (!Schema::hasColumn('quotes', 'additional_terms')) {
                $table->text('additional_terms')->nullable()->after('validity_details');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            $columns = [
                'system_type', 'mounting_type', 'expert_rfi_mounting',
                'inverter_cost', 'mounting_structure_cost', 'transformers_labor_cost',
                'permits_documentation_cost', 'gst_cost',
                'full_payment', 'government_subsidy', 'net_amount_after_subsidy',
                'estimated_payback_period', 'monthly_energy_generation_kwh', 'monthly_savings',
                'annual_savings', 'year_total_savings', 'warranty_details', 'installation_timeline',
                'payment_terms_detailed', 'validity_details', 'additional_terms'
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('quotes', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
