<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('urjamarts', function (Blueprint $table) {
            $table->id();
            $table->string('firm_name');
            $table->string('email')->unique();
            $table->string('mobile', 15);
            $table->string('business_products');
            $table->string('business_type');
            $table->string('pan', 10);
            $table->string('aadhaar', 12);
            $table->string('gstin', 15)->nullable();
            $table->string('pan_doc')->nullable();
            $table->string('aadhaar_doc')->nullable();
            $table->string('gst_doc')->nullable();
            $table->string('shop_name');
            $table->string('shop_photo')->nullable();
            $table->string('owner_photo')->nullable();
            $table->string('address1');
            $table->string('address2')->nullable();
            $table->string('city');
            $table->string('state');
            $table->string('pincode');
            $table->string('nearest_police_station')->nullable();
            $table->string('shop_type');
            $table->string('owner_name');
            $table->string('account_holder_name')->nullable();
            $table->string('account_number')->nullable();
            $table->string('ifsc_code')->nullable();
            $table->string('bank_name')->nullable();
            $table->string('password');
            $table->rememberToken();
            $table->timestamps();
        });
    }
    public function down(): void
    {
        Schema::dropIfExists('urjamarts');
    }
};
