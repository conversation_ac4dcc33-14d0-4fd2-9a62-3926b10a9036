<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('full_name');
            $table->string('mobile', 15);
            $table->string('email')->nullable();
            $table->string('age_group')->nullable();
            $table->string('address');
            $table->string('city');
            $table->string('pincode');
            $table->string('service_type');
            $table->integer('monthly_bill')->nullable();
            $table->string('load_requirement');
            $table->string('installation_timeline')->nullable();
            $table->string('roof_type');
            $table->integer('roof_space')->nullable();
            $table->string('shadow_issues')->nullable();
            $table->string('property_type')->nullable();
            $table->string('latitude')->nullable();
            $table->string('longitude')->nullable();
            $table->string('customer_photo')->nullable();
            $table->string('bill_photo')->nullable();
            $table->string('roof_photo')->nullable();
            $table->string('property_photo')->nullable();
            $table->string('interest')->nullable();
            $table->text('notes')->nullable();
            $table->string('inserted_by_usertype');
            $table->unsignedBigInteger('inserted_by_userid');
            $table->timestamps();
        });
    }
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
