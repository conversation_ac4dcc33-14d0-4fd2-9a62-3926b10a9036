<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Usertype;

class SignupController extends Controller
{
    public function showSignupForm()
    {
        $usertypes = Usertype::where('is_active', true)->get();
        return view('auth.signup', compact('usertypes'));
    }

    public function submitSignup(Request $request)
    {
        $usertype = $request->input('usertype');
        if ($usertype === 'admin') {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255|unique:admins,email',
                'mobile' => 'required|string|max:15',
                'department' => 'required|string',
                'password' => 'required|string|min:6|confirmed',
            ]);
            try {
                $admin = new \App\Models\Admin();
                $admin->name = $validated['name'];
                $admin->email = $validated['email'];
                $admin->mobile = $validated['mobile'];
                $admin->department = $validated['department'];
                $admin->aadhaar = $request->input('aadhaar');
                $admin->pan = $request->input('pan');
                $admin->gstin = $request->input('gstin');
                $admin->password = bcrypt($validated['password']);
                // Handle file uploads
                foreach(['aadhaar_doc','pan_doc','gst_doc','admin_photo'] as $field) {
                    if ($request->hasFile($field)) {
                        $admin->$field = $request->file($field)->store('uploads', 'public');
                    }
                }
                $admin->save();
            } catch (\Exception $e) {
                return response()->json(['success' => false, 'message' => $e->getMessage()], 400);
            }
        } elseif ($usertype === 'urjamart') {
            $urjamart = new \App\Models\Urjamart();
            $urjamart->firm_name = $request->input('firm_name');
            $urjamart->email = $request->input('email');
            $urjamart->mobile = $request->input('mobile');
            $urjamart->business_products = $request->input('business_products');
            $urjamart->business_type = $request->input('business_type');
            $urjamart->pan = $request->input('pan');
            $urjamart->aadhaar = $request->input('aadhaar');
            $urjamart->gstin = $request->input('gstin');
            $urjamart->shop_name = $request->input('shop_name');
            $urjamart->address1 = $request->input('address1');
            $urjamart->address2 = $request->input('address2');
            $urjamart->city = $request->input('city');
            $urjamart->state = $request->input('state');
            $urjamart->pincode = $request->input('pincode');
            $urjamart->nearest_police_station = $request->input('nearest_police_station');
            $urjamart->shop_type = $request->input('shop_type');
            $urjamart->owner_name = $request->input('owner_name');
            $urjamart->account_holder_name = $request->input('account_holder_name');
            $urjamart->account_number = $request->input('account_number');
            $urjamart->ifsc_code = $request->input('ifsc_code');
            $urjamart->bank_name = $request->input('bank_name');
            $urjamart->password = bcrypt($request->input('password'));
            foreach(['pan_doc','aadhaar_doc','gst_doc','shop_photo','owner_photo'] as $field) {
                if ($request->hasFile($field)) {
                    $urjamart->$field = $request->file($field)->store('uploads', 'public');
                }
            }
            $urjamart->save();
        } elseif ($usertype === 'urjamitra') {
            $urjamitra = new \App\Models\Urjamitra();
            $urjamitra->name = $request->input('name');
            $urjamitra->father_name = $request->input('father_name');
            $urjamitra->age = $request->input('age');
            $urjamitra->mobile = $request->input('mobile');
            $urjamitra->email = $request->input('email');
            $urjamitra->business_profile = $request->input('business_profile');
            $urjamitra->address = $request->input('address');
            $urjamitra->aadhaar = $request->input('aadhaar');
            $urjamitra->pan = $request->input('pan');
            $urjamitra->gstin = $request->input('gstin');
            $urjamitra->password = bcrypt($request->input('password'));
            foreach(['aadhaar_doc','pan_doc','gst_doc'] as $field) {
                if ($request->hasFile($field)) {
                    $urjamitra->$field = $request->file($field)->store('uploads', 'public');
                }
            }
            $urjamitra->save();
        }
        return response()->json(['success' => true]);
    }
}
