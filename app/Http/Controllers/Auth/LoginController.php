<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LoginController extends Controller
{
    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $usertype = $request->input('usertype');
        $identifier = $request->input('mobile'); // Use mobile field from form
        $password = $request->input('password');

        $user = null;
        if ($usertype === 'superadmin') {
            $user = \App\Models\Superadmin::where('email', $identifier)->orWhere('mobile', $identifier)->first();
        } elseif ($usertype === 'admin') {
            // Check both email and mobile for admin login
            $user = \App\Models\Admin::where('email', $identifier)->orWhere('mobile', $identifier)->first();
        } elseif ($usertype === 'urjamart') {
            $user = \App\Models\Urjamart::where('email', $identifier)->first();
        } elseif ($usertype === 'urjamitra') {
            $user = \App\Models\Urjamitra::where('email', $identifier)->first();
        }

        if ($user && \Hash::check($password, $user->password)) {
            session(['userdetails' => $user, 'usertype' => $usertype]);
            return response()->json([
                'success' => true,
                'redirect' => url('/dashboard'),
                'message' => 'Login successful!'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials or user type.'
            ], 401);
        }
    }
}
