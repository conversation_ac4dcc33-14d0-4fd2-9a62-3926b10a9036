<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;

class QuoteController extends Controller
{
    public function showForm($customer_id = null)
    {
        $usertype = session('usertype');
        $userdetails = session('userdetails');
        $customers = Customer::orderBy('full_name')->get();
        return view('quote_form', [
            'usertype' => $usertype,
            'Userdetails' => $userdetails,
            'customers' => $customers,
            'selected_customer' => $customer_id
        ]);
    }

    public function submit(Request $request)
    {
        // TODO: Save quote data to database
        return redirect('/survey')->with('success', 'Quotation sent successfully!');
    }
}
