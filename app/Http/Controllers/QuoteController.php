<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\Quote;

class QuoteController extends Controller
{
    public function index()
    {
        $usertype = session('usertype');
        $userdetails = session('userdetails');
        $quotes = Quote::with('customer')->orderBy('created_at', 'desc')->get();
        return view('quote_list', [
            'usertype' => $usertype,
            'Userdetails' => $userdetails,
            'quotes' => $quotes
        ]);
    }

    public function showForm($customer_id = null)
    {
        $usertype = session('usertype');
        $userdetails = session('userdetails');
        $customers = Customer::orderBy('full_name')->get();
        $customer = $customer_id ? Customer::find($customer_id) : null;

        return view('quote_form', [
            'usertype' => $usertype,
            'Userdetails' => $userdetails,
            'customers' => $customers,
            'selected_customer' => $customer_id,
            'customer' => $customer
        ]);
    }

    public function view($id)
    {
        $usertype = session('usertype');
        $userdetails = session('userdetails');
        $quote = Quote::with('customer')->findOrFail($id);
        return view('quote_view', [
            'usertype' => $usertype,
            'Userdetails' => $userdetails,
            'quote' => $quote
        ]);
    }

    public function submit(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'system_capacity' => 'required|numeric|min:0',
            'panel_count' => 'required|integer|min:1',
            'panel_type' => 'required|string',
            'panel_wattage' => 'required|numeric|min:0',
            'inverter_type' => 'required|string',
            'inverter_capacity' => 'required|numeric|min:0',
            'mounting_structure' => 'required|string',
            'system_cost' => 'required|numeric|min:0',
            'installation_cost' => 'required|numeric|min:0',
            'subsidy_amount' => 'nullable|numeric|min:0',
            'terms_conditions' => 'nullable|string',
            'notes' => 'nullable|string',
            'valid_until' => 'required|date|after:today'
        ]);

        $total_cost = $validated['system_cost'] + $validated['installation_cost'];
        $final_cost = $total_cost - ($validated['subsidy_amount'] ?? 0);

        $quote = new Quote();
        $quote->fill($validated);
        $quote->total_cost = $total_cost;
        $quote->final_cost = $final_cost;
        $quote->created_by_usertype = session('usertype');
        $quote->created_by_userid = session('userdetails')['id'] ?? null;
        $quote->save();

        return response()->json(['success' => true, 'redirect' => url('/quotation')]);
    }
}
