<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\Quote;

class QuoteController extends Controller
{
    public function index()
    {
        $usertype = session('usertype');
        $userdetails = session('userdetails');
        $quotes = Quote::with('customer')->orderBy('created_at', 'desc')->get();
        return view('quote_list', [
            'usertype' => $usertype,
            'Userdetails' => $userdetails,
            'quotes' => $quotes
        ]);
    }

    public function showForm($customer_id = null)
    {
        $usertype = session('usertype');
        $userdetails = session('userdetails');
        $customers = Customer::orderBy('full_name')->get();
        $customer = $customer_id ? Customer::find($customer_id) : null;

        return view('quote_form', [
            'usertype' => $usertype,
            'Userdetails' => $userdetails,
            'customers' => $customers,
            'selected_customer' => $customer_id,
            'customer' => $customer
        ]);
    }

    public function view($id)
    {
        $usertype = session('usertype');
        $userdetails = session('userdetails');
        $quote = Quote::with('customer')->findOrFail($id);
        return view('quote_view', [
            'usertype' => $usertype,
            'Userdetails' => $userdetails,
            'quote' => $quote
        ]);
    }

    public function submit(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'system_capacity' => 'required|numeric|min:0',
            'panel_count' => 'required|integer|min:1',
            'panel_type' => 'required|string',
            'inverter_type' => 'required|string',
            'mounting_type' => 'required|string',
            'valid_until' => 'required|date|after:today'
        ]);

        // Calculate costs from the detailed breakdown
        $panel_total = $request->panel_total_cost ?? 0;
        $inverter_cost = $request->inverter_cost ?? 0;
        $mounting_cost = $request->mounting_structure_cost ?? 0;
        $labor_cost = $request->transformers_labor_cost ?? 0;
        $permits_cost = $request->permits_documentation_cost ?? 0;

        $subtotal = $panel_total + $inverter_cost + $mounting_cost + $labor_cost + $permits_cost;
        $subsidy_amount = $request->subsidy_amount ?? 0;
        $net_amount = $subtotal - $subsidy_amount;
        $gst_cost = $net_amount * 0.18;
        $total_cost = $net_amount + $gst_cost;
        $final_cost = $total_cost;

        $quote = new Quote();
        $quote->customer_id = $validated['customer_id'];
        $quote->system_capacity = $validated['system_capacity'];
        $quote->system_type = $request->system_type;
        $quote->panel_count = $validated['panel_count'];
        $quote->panel_type = $validated['panel_type'];
        $quote->panel_wattage = $request->panel_wattage ?? 0;
        $quote->inverter_type = $validated['inverter_type'];
        $quote->inverter_capacity = $request->inverter_capacity ?? 0;
        $quote->mounting_structure = $request->mounting_structure ?? 'Roof Mount';
        $quote->mounting_type = $validated['mounting_type'];
        $quote->expert_rfi_mounting = $request->has('expert_rfi_mounting');

        // Cost breakdown
        $quote->system_cost = $subtotal;
        $quote->installation_cost = $labor_cost;
        $quote->total_cost = $total_cost;
        $quote->subsidy_amount = $subsidy_amount;
        $quote->final_cost = $final_cost;
        $quote->inverter_cost = $inverter_cost;
        $quote->mounting_structure_cost = $mounting_cost;
        $quote->transformers_labor_cost = $labor_cost;
        $quote->permits_documentation_cost = $permits_cost;
        $quote->gst_cost = $gst_cost;

        // Financial information
        $quote->full_payment = $request->full_payment;
        $quote->government_subsidy = $request->government_subsidy;
        $quote->net_amount_after_subsidy = $request->net_amount_after_subsidy;
        $quote->estimated_payback_period = $request->estimated_payback_period;

        // Savings information
        $quote->monthly_energy_generation_kwh = $request->monthly_energy_generation_kwh;
        $quote->monthly_savings = $request->monthly_savings;
        $quote->annual_savings = $request->annual_savings;
        $quote->year_total_savings = $request->year_total_savings;

        // Terms & conditions
        $quote->warranty_details = $request->warranty_details;
        $quote->installation_timeline = $request->installation_timeline;
        $quote->payment_terms_detailed = $request->payment_terms_detailed;
        $quote->validity_details = $request->validity_details;
        $quote->additional_terms = $request->additional_terms;
        $quote->terms_conditions = $request->terms_conditions;
        $quote->notes = $request->notes;
        $quote->valid_until = $validated['valid_until'];
        $quote->created_by_usertype = session('usertype');
        $quote->created_by_userid = session('userdetails')['id'] ?? null;

        $quote->save();

        return response()->json([
            'success' => true,
            'message' => 'Comprehensive quotation created successfully!',
            'redirect' => url('/quotation')
        ]);
    }
}
