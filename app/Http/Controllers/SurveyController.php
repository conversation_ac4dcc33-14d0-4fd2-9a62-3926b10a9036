<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;

class SurveyController extends Controller
{
    public function showForm()
    {
        $usertype = session('usertype');
        $userdetails = session('userdetails');
        return view('survey_form', [
            'usertype' => $usertype,
            'Userdetails' => $userdetails
        ]);
    }

    public function index()
    {
        $usertype = session('usertype');
        $userdetails = session('userdetails');
        $customers = Customer::orderBy('created_at', 'desc')->get();
        return view('survey', [
            'usertype' => $usertype,
            'Userdetails' => $userdetails,
            'customers' => $customers
        ]);
    }

    public function view($id)
    {
        $usertype = session('usertype');
        $userdetails = session('userdetails');
        $customer = Customer::findOrFail($id);
        return view('survey_view', [
            'usertype' => $usertype,
            'Userdetails' => $userdetails,
            'customer' => $customer
        ]);
    }

    public function submit(Request $request)
    {
        $validated = $request->validate([
            'full_name' => 'required',
            'mobile' => 'required',
            'address' => 'required',
            'city' => 'required',
            'pincode' => 'required',
            'service_type' => 'required',
            'load_requirement' => 'required',
            'roof_type' => 'required',
            'interest' => 'required',
        ]);
        $customer = new Customer();
        $customer->fill($request->except(['_token']));
        $customer->inserted_by_usertype = session('usertype');
        $customer->inserted_by_userid = session('userdetails')['id'] ?? null;
        // Handle file uploads
        foreach(['customer_photo','bill_photo','roof_photo','property_photo'] as $field) {
            if ($request->hasFile($field)) {
                $customer->$field = $request->file($field)->store('uploads', 'public');
            }
        }
        $customer->save();
        return response()->json(['success' => true, 'redirect' => url('/survey')]);
    }
}
