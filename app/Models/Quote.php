<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Quote extends Model
{
    protected $fillable = [
        'customer_id',
        'quote_number',
        'system_capacity',
        'system_type',
        'panel_count',
        'panel_type',
        'panel_wattage',
        'inverter_type',
        'inverter_capacity',
        'mounting_structure',
        'mounting_type',
        'expert_rfi_mounting',
        'system_cost',
        'installation_cost',
        'total_cost',
        'subsidy_amount',
        'final_cost',
        'inverter_cost',
        'mounting_structure_cost',
        'transformers_labor_cost',
        'permits_documentation_cost',
        'gst_cost',
        'full_payment',
        'government_subsidy',
        'net_amount_after_subsidy',
        'estimated_payback_period',
        'monthly_energy_generation_kwh',
        'monthly_savings',
        'annual_savings',
        'year_total_savings',
        'warranty_details',
        'installation_timeline',
        'payment_terms_detailed',
        'validity_details',
        'additional_terms',
        'terms_conditions',
        'notes',
        'status',
        'valid_until',
        'created_by_usertype',
        'created_by_userid'
    ];

    protected $casts = [
        'valid_until' => 'date',
        'expert_rfi_mounting' => 'boolean',
        'system_capacity' => 'decimal:2',
        'panel_wattage' => 'decimal:2',
        'inverter_capacity' => 'decimal:2',
        'system_cost' => 'decimal:2',
        'installation_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'subsidy_amount' => 'decimal:2',
        'final_cost' => 'decimal:2',
        'inverter_cost' => 'decimal:2',
        'mounting_structure_cost' => 'decimal:2',
        'transformers_labor_cost' => 'decimal:2',
        'permits_documentation_cost' => 'decimal:2',
        'gst_cost' => 'decimal:2',
        'full_payment' => 'decimal:2',
        'government_subsidy' => 'decimal:2',
        'net_amount_after_subsidy' => 'decimal:2',
        'estimated_payback_period' => 'decimal:2',
        'monthly_energy_generation_kwh' => 'decimal:2',
        'monthly_savings' => 'decimal:2',
        'annual_savings' => 'decimal:2',
        'year_total_savings' => 'decimal:2'
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($quote) {
            if (empty($quote->quote_number)) {
                $quote->quote_number = 'QT' . date('Y') . str_pad(static::count() + 1, 4, '0', STR_PAD_LEFT);
            }
        });
    }
}
