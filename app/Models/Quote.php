<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Quote extends Model
{
    protected $fillable = [
        'customer_id',
        'quote_number',
        'system_capacity',
        'panel_count',
        'panel_type',
        'panel_wattage',
        'inverter_type',
        'inverter_capacity',
        'mounting_structure',
        'system_cost',
        'installation_cost',
        'total_cost',
        'subsidy_amount',
        'final_cost',
        'terms_conditions',
        'notes',
        'status',
        'valid_until',
        'created_by_usertype',
        'created_by_userid'
    ];

    protected $casts = [
        'valid_until' => 'date',
        'system_capacity' => 'decimal:2',
        'panel_wattage' => 'decimal:2',
        'inverter_capacity' => 'decimal:2',
        'system_cost' => 'decimal:2',
        'installation_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'subsidy_amount' => 'decimal:2',
        'final_cost' => 'decimal:2'
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($quote) {
            if (empty($quote->quote_number)) {
                $quote->quote_number = 'QT' . date('Y') . str_pad(static::count() + 1, 4, '0', STR_PAD_LEFT);
            }
        });
    }
}
