<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    protected $fillable = [
        'full_name','mobile','email','age_group','address','city','pincode','service_type','monthly_bill','load_requirement','installation_timeline','roof_type','roof_space','shadow_issues','property_type','latitude','longitude','customer_photo','bill_photo','roof_photo','property_photo','interest','notes','inserted_by_usertype','inserted_by_userid'
    ];

    public function quotes(): HasMany
    {
        return $this->hasMany(Quote::class);
    }
}
