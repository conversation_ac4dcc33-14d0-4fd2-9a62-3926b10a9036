<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Urja Channel Partner Login</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f0f2f5;
            margin: 0;
        }
        .login-container {
            background-color: #fff;
            padding: 2rem 3rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
            width: 100%;
            max-width: 400px;
        }
        .logo {
            margin-bottom: 1.5rem;
        }
        .logo img {
            width: 100px;
        }
        h1 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #333;
        }
        p {
            color: #666;
            margin-bottom: 2rem;
        }
        .input-group {
            position: relative;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
        }
        .input-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            z-index: 2;
        }
        .input-group input,
        .input-group select {
            width: 100%;
            padding: 1rem 1rem 1rem 40px;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 1rem;
            background: #f7f9fa;
            margin: 0;
        }
        .input-group select {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
        }
        .options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        .options a {
            color: #007bff;
            text-decoration: none;
        }
        .btn {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin-bottom: 1rem;
        }
        .btn-primary {
            background-color: #007bff;
            color: #fff;
        }
        .btn-secondary {
            background-color: #fff;
            color: #007bff;
            border: 1px solid #007bff;
        }
        .divider {
            margin: 1.5rem 0;
            color: #999;
        }
    </style>
</head>
<body>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <div class="login-container">
        <div class="logo">
            <img src="{{ asset('images/urjamart-logo.png') }}" alt="Urjamart Logo" style="height:60px;">
        </div>
        <h1>Login</h1>
        <p>Enter your credentials to access the portal</p>
        <form id="loginForm" action="{{ url('/login') }}" method="post">
            @csrf
            <div class="input-group">
                <i class="fas fa-user-tag"></i>
                <select name="usertype" required>
                    <option value="">Select User Type</option>
                    <option value="superadmin">Superadmin</option>
                    <option value="admin">Admin</option>
                    <option value="urjamart">Urjamart</option>
                    <option value="urjamitra">Urjamitra</option>
                </select>
            </div>
            <div class="input-group">
                <i class="fas fa-phone-alt"></i>
                <input type="text" name="mobile" placeholder="Enter your mobile number" required>
            </div>
            <div class="input-group">
                <i class="fas fa-lock"></i>
                <input type="password" name="password" placeholder="Enter your password" required>
            </div>
            <div class="options">
                <div>
                    <input type="checkbox" id="remember">
                    <label for="remember">Remember me</label>
                </div>
                <a href="#">Forgot Password?</a>
            </div>
            <button type="submit" class="btn btn-primary"><i class="fas fa-sign-in-alt"></i> Sign In</button>
            <div class="divider">or</div>
            <a href="{{ url('/signup') }}" class="btn btn-secondary" style="display:block;text-align:center;"><i class="fas fa-user-plus"></i> Create New Account</a>
        </form>
    </div>
    <script>
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        var form = this;
        var formData = new FormData(form);
        Swal.fire({
            title: 'Authenticating...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(async response => {
            let data;
            try {
                data = await response.json();
            } catch (e) {
                const text = await response.text();
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: text || 'An error occurred. Please try again.',
                    confirmButtonText: 'OK'
                });
                return;
            }
            if (response.ok && data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Login Successful!',
                    text: data.message || 'You are being redirected...',
                    timer: 1500,
                    showConfirmButton: false,
                    willClose: () => {
                        window.location.href = data.redirect;
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Login Failed',
                    text: (data && data.message) ? data.message : 'Invalid credentials. Please try again.',
                    confirmButtonText: 'OK'
                });
            }
        })
        .catch(() => {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Network error. Please try again.',
                confirmButtonText: 'OK'
            });
        });
    });
    </script>
</body>
</html>
