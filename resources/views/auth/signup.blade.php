@extends('layouts.app')

@section('content')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
    body {
        background-color: #fff !important;
    }
    .required {
        color: red;
        font-weight: bold;
        margin-left: 2px;
    }
</style>
<div class="container">
    <div class="text-center mb-3">
        <img src="{{ asset('images/urjamart-logo.png') }}" alt="Urjamart Logo" style="height:60px;">
    </div>
    <h2 class="text-center mb-2">Sign Up</h2>
    <p class="text-center mb-4">Please select your user type to begin registration. The form will change based on your selection.</p>
    <div class="card p-4 mb-4" style="max-width:500px;margin:auto;">
        <form id="usertypeForm">
            <div class="mb-3">
                <label for="usertype" class="form-label">Select User Type <span class="required">*</span></label>
                <select name="usertype" id="usertype" class="form-control" required>
                    <option value="">Select User Type</option>
                    <option value="admin">Admin</option>
                    <option value="urjamart">Urjamart</option>
                    <option value="urjamitra">Urja Mitra</option>
                </select>
            </div>
        </form>
    </div>
    <div id="urjamartForm" style="display:none;">
        <form method="POST" action="{{ url('/signup') }}" enctype="multipart/form-data">
            <input type="hidden" name="usertype" value="urjamart">
            @csrf
            <!-- Urjamart form fields start -->
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Organization Details</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Firm / Channel Partner Name <span class="required">*</span></label>
                        <input type="text" name="firm_name" class="form-control" placeholder="Firm / Channel Partner Name" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Email address <span class="required">*</span></label>
                        <input type="email" name="email" class="form-control" placeholder="Email address" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Mobile <span class="required">*</span></label>
                        <input type="text" name="mobile" class="form-control" placeholder="10-digit mobile" required maxlength="10">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Business Products <span class="required">*</span></label>
                        <input type="text" name="business_products" class="form-control" placeholder="Business Products (e.g., Solar panels)" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Type of Business <span class="required">*</span></label>
                        <input type="text" name="business_type" class="form-control" placeholder="Type of Business" required>
                    </div>
                    <div class="col-md-6">
                            <label class="form-label">PAN (**********) <span class="required">*</span></label>
                            <input type="text" name="pan" class="form-control mb-2" placeholder="PAN (**********)" required maxlength="10">
                            <label class="form-label">PAN Document <span class="required">*</span></label>
                            <input type="file" name="pan_doc" class="form-control" accept=".pdf,.jpg,.jpeg,.png" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                            <label class="form-label">Aadhaar (12 digits) <span class="required">*</span></label>
                            <input type="text" name="aadhaar" class="form-control mb-2" placeholder="Aadhaar (12 digits)" required maxlength="12">
                            <label class="form-label">Aadhaar Document <span class="required">*</span></label>
                            <input type="file" name="aadhaar_doc" class="form-control" accept=".pdf,.jpg,.jpeg,.png" required>
                    </div>
                    <div class="col-md-6">
                            <label class="form-label">GSTIN (15 CHARACTERS)</label>
                            <input type="text" name="gstin" class="form-control mb-2" placeholder="GSTIN (15 CHARACTERS)" maxlength="15">
                            <label class="form-label">GST Document</label>
                            <input type="file" name="gst_doc" class="form-control" accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Shop Name <span class="required">*</span></label>
                        <input type="text" name="shop_name" class="form-control" placeholder="Shop Name" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Shop Photo <span class="required">*</span></label>
                        <input type="file" name="shop_photo" class="form-control" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Owner Photo <span class="required">*</span></label>
                        <input type="file" name="owner_photo" class="form-control" required>
                    </div>
                </div>
            </div>
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Office Address</h5>
                <div class="mb-3">
                    <input type="text" name="address1" class="form-control" placeholder="Address Line 1" required>
                </div>
                <div class="mb-3">
                    <input type="text" name="address2" class="form-control" placeholder="Address Line 2 (Optional)">
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <input type="text" name="city" class="form-control" placeholder="City" required>
                    </div>
                    <div class="col-md-4">
                        <input type="text" name="state" class="form-control" placeholder="State" required>
                    </div>
                    <div class="col-md-4">
                        <input type="text" name="pincode" class="form-control" placeholder="Pincode" required maxlength="6">
                    </div>
                </div>
                <div class="mb-3">
                    <input type="text" name="nearest_police_station" class="form-control" placeholder="Nearest Police Station">
                </div>
                <div class="mb-3">
                    <label>Shop Type</label>
                    <div>
                        <input type="radio" name="shop_type" value="own" checked> Own
                        <input type="radio" name="shop_type" value="rental"> Rental
                    </div>
                </div>
            </div>
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Owner Details</h5>
                <div class="mb-3">
                    <input type="text" name="owner_name" class="form-control" placeholder="Owner Name" required>
                </div>
            </div>
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Bank Details</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Account Holder Name</label>
                        <input type="text" name="account_holder_name" class="form-control" placeholder="Account Holder Name">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Account Number</label>
                        <input type="text" name="account_number" class="form-control" placeholder="Account Number">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">IFSC CODE</label>
                        <input type="text" name="ifsc_code" class="form-control" placeholder="IFSC CODE">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Bank Name</label>
                        <input type="text" name="bank_name" class="form-control" placeholder="Bank Name">
                    </div>
                </div>
            </div>
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Account Security</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <input type="password" name="password" class="form-control" placeholder="Password" required>
                    </div>
                    <div class="col-md-6">
                        <input type="password" name="password_confirmation" class="form-control" placeholder="Confirm Password" required>
                    </div>
                </div>
            </div>
            <!-- Removed duplicate Documents Upload section -->
            <button type="submit" class="btn btn-primary w-100 mb-3">Create Channel Partner Account</button>
        </form>
    </div>
    <div id="adminForm" style="display:none;">
        <form method="POST" action="{{ url('/signup') }}" enctype="multipart/form-data">
            <input type="hidden" name="usertype" value="admin">
            @csrf
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Admin Details</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Full Name <span class="required">*</span></label>
                        <input type="text" name="name" class="form-control" placeholder="Full Name" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Email Address <span class="required">*</span></label>
                        <input type="email" name="email" class="form-control" placeholder="Email address" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Mobile <span class="required">*</span></label>
                        <input type="text" name="mobile" class="form-control" placeholder="10-digit mobile" required maxlength="10">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Department <span class="required">*</span></label>
                        <select name="department" class="form-control" required>
                            <option value="">Select Department</option>
                            <option value="project">Project</option>
                            <option value="sales">Sales</option>
                            <option value="account">Account</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label class="form-label">Aadhaar Number</label>
                        <input type="text" name="aadhaar" class="form-control mb-2" placeholder="Aadhaar (12 digits)" maxlength="12">
                        <label class="form-label">Aadhaar Document</label>
                        <input type="file" name="aadhaar_doc" class="form-control" accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">PAN Number</label>
                        <input type="text" name="pan" class="form-control mb-2" placeholder="PAN (**********)" maxlength="10">
                        <label class="form-label">PAN Document</label>
                        <input type="file" name="pan_doc" class="form-control" accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">GSTIN</label>
                        <input type="text" name="gstin" class="form-control mb-2" placeholder="GSTIN (15 CHARACTERS)" maxlength="15">
                        <label class="form-label">GST Document</label>
                        <input type="file" name="gst_doc" class="form-control" accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Photo</label>
                        <input type="file" name="admin_photo" class="form-control" accept=".jpg,.jpeg,.png">
                    </div>
                </div>
            </div>
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Account Security</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Password <span class="required">*</span></label>
                        <input type="password" name="password" class="form-control" placeholder="Password" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Confirm Password <span class="required">*</span></label>
                        <input type="password" name="password_confirmation" class="form-control" placeholder="Confirm Password" required>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-primary w-100 mb-3">Create Admin Account</button>
        </form>
    </div>
    <div id="urjamitraForm" style="display:none;">
        <form method="POST" action="{{ url('/signup') }}" enctype="multipart/form-data">
            <input type="hidden" name="usertype" value="urjamitra">
            @csrf
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Personal Details</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Full Name <span class="required">*</span></label>
                        <input type="text" name="name" class="form-control" placeholder="Full Name" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Father's Name <span class="required">*</span></label>
                        <input type="text" name="father_name" class="form-control" placeholder="Father's Name" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Age <span class="required">*</span></label>
                        <input type="number" name="age" class="form-control" placeholder="Age" required min="18">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Mobile <span class="required">*</span></label>
                        <input type="text" name="mobile" class="form-control" placeholder="10-digit mobile" required maxlength="10">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Email address <span class="required">*</span></label>
                        <input type="email" name="email" class="form-control" placeholder="Email address" required>
                    </div>
                </div>
            </div>
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Business Profile</h5>
                <input type="text" name="business_profile" class="form-control mb-3" placeholder="e.g., Freelancer, Agent, Surveyor" required>
            </div>
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Address</h5>
                <textarea name="address" class="form-control mb-3" placeholder="Enter your complete address" required></textarea>
            </div>
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Identity Details (Optional)</h5>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <input type="text" name="aadhaar" class="form-control mb-2" placeholder="Aadhaar Number (12 digits)" maxlength="12">
                        <label class="form-label">Aadhaar Document</label>
                        <input type="file" name="aadhaar_doc" class="form-control" accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                    <div class="col-md-4">
                        <input type="text" name="pan" class="form-control mb-2" placeholder="PAN (**********)" maxlength="10">
                        <label class="form-label">PAN Document</label>
                        <input type="file" name="pan_doc" class="form-control" accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                    <div class="col-md-4">
                        <input type="text" name="gstin" class="form-control mb-2" placeholder="GSTIN (15 CHARACTERS)" maxlength="15">
                        <label class="form-label">GST Document</label>
                        <input type="file" name="gst_doc" class="form-control" accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                </div>
            </div>
            <div class="card p-4 mb-4">
                <h5 class="mb-3 text-primary">Account Security</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <input type="password" name="password" class="form-control" placeholder="Password" required>
                    </div>
                    <div class="col-md-6">
                        <input type="password" name="password_confirmation" class="form-control" placeholder="Confirm Password" required>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-primary w-100 mb-3">Create Urja Mitra Account</button>
        </form>
    </div>
    <div class="text-center mt-4">
        <a href="{{ route('login') }}" class="btn btn-outline-primary">Already have an account? Sign In</a>
    </div>
</div>
<script>
    document.getElementById('usertype').addEventListener('change', function() {
        var selected = this.value;
        document.getElementById('urjamartForm').style.display = (selected === 'urjamart') ? 'block' : 'none';
        document.getElementById('adminForm').style.display = (selected === 'admin') ? 'block' : 'none';
        document.getElementById('urjamitraForm').style.display = (selected === 'urjamitra') ? 'block' : 'none';
    });

    // SweetAlert for form submission with AJAX
    document.querySelectorAll('form[action="{{ url('/signup') }}"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            var formData = new FormData(form);
            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(async response => {
                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    // If not JSON, show raw response
                    const text = await response.text();
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: text || 'An error occurred. Please try again.',
                        confirmButtonText: 'OK'
                    });
                    return;
                }
                if (response.ok && data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Registration Submitted!',
                        text: 'Your registration has been submitted successfully.',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        window.location.href = '/login';
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: (data && data.message) ? data.message : 'An error occurred. Please try again.',
                        confirmButtonText: 'OK'
                    });
                }
            })
        });
    });
</script>
@endsection