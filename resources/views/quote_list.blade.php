@extends('layouts.app')
@section('content')
@include('components.header', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
<div style="max-width:420px;margin:30px auto 70px auto;position:relative;">
    <!-- Big + button at bottom right -->
    <a href="{{ url('/quote/form') }}" class="quote-plus-btn" style="position:fixed;bottom:88px;right:20px;width:56px;height:56px;background:linear-gradient(135deg,#28a745 60%,#20c997 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 16px rgba(0,0,0,0.18);color:#fff;font-size:2.1rem;z-index:200;transition:background 0.2s;">
        <i class="fa fa-plus"></i>
    </a>
    <div class="quote-list">
        @if(count($quotes) === 0)
            <div style="text-align:center;padding:2.5rem 1rem;color:#888;">
                <i class="fa fa-file-invoice" style="font-size:2.5rem;color:#28a745;margin-bottom:10px;"></i>
                <div style="font-size:1.15rem;font-weight:600;margin-bottom:6px;">No quotations found</div>
                <div style="font-size:0.98rem;">Create your first quotation by clicking the <b>+</b> button below!</div>
            </div>
        @else
            @foreach($quotes as $quote)
            <div class="quote-card" style="background:#fff;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.07);padding:1.2rem 1rem;margin-bottom:1.2rem;">
                <div style="display:flex;justify-content:space-between;align-items:start;margin-bottom:8px;">
                    <div style="font-weight:600;font-size:1.08rem;color:#333;">
                        {{ $quote->customer->full_name }}
                    </div>
                    <div style="text-align:right;">
                        <div style="background:{{ $quote->status == 'sent' ? '#e3f7ff' : ($quote->status == 'accepted' ? '#d4edda' : ($quote->status == 'rejected' ? '#f8d7da' : '#f8f9fa')) }};color:{{ $quote->status == 'sent' ? '#007bff' : ($quote->status == 'accepted' ? '#155724' : ($quote->status == 'rejected' ? '#721c24' : '#6c757d')) }};font-size:0.8rem;padding:2px 8px;border-radius:6px;margin-bottom:2px;">
                            {{ ucfirst($quote->status) }}
                        </div>
                        <div style="font-size:0.8rem;color:#666;">{{ $quote->quote_number }}</div>
                    </div>
                </div>
                <div style="font-size:0.97rem;color:#666;margin:2px 0;">📞 {{ $quote->customer->mobile }}</div>
                <div style="font-size:0.97rem;color:#666;margin:2px 0;">📍 {{ $quote->customer->city }}, {{ $quote->customer->address }}</div>
                <div style="font-size:0.97rem;color:#666;margin:2px 0;">⚡ {{ $quote->system_capacity }} KW • {{ $quote->panel_count }} Panels</div>
                <div style="font-size:0.97rem;color:#666;margin:2px 0;">💰 ₹{{ number_format($quote->final_cost, 0) }}</div>
                <div style="font-size:0.97rem;color:#666;margin:2px 0;">📅 Valid until: {{ $quote->valid_until->format('d M Y') }}</div>
                <div style="margin-top:10px;display:flex;gap:10px;">
                    <a href="{{ url('/quote/view/'.$quote->id) }}" class="btn btn-primary" style="flex:1;">View Details</a>
                    <a href="{{ url('/quote/form/'.$quote->customer_id) }}" class="btn btn-secondary" style="flex:1;">Edit</a>
                </div>
            </div>
            @endforeach
        @endif
    </div>
</div>
@include('components.bottomtab', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
<style>
.quote-plus-btn:hover { background: #1e7e34; box-shadow:0 6px 20px rgba(40,167,69,0.22); }
</style>
@endsection
