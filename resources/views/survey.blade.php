@extends('layouts.app')
@section('content')
@include('components.header', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
<div style="max-width:420px;margin:30px auto 70px auto;position:relative;">
    <!-- Big + button at bottom right -->
    <a href="{{ url('/survey/form') }}" class="survey-plus-btn" style="position:fixed;bottom:88px;right:20px;width:56px;height:56px;background:linear-gradient(135deg,#007bff 60%,#00c6ff 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 16px rgba(0,0,0,0.18);color:#fff;font-size:2.1rem;z-index:200;transition:background 0.2s;">
        <i class="fa fa-plus"></i>
    </a>
    <div class="survey-list">
        @if(count($customers) === 0)
            <div style="text-align:center;padding:2.5rem 1rem;color:#888;">
                <i class="fa fa-folder-open" style="font-size:2.5rem;color:#007bff;margin-bottom:10px;"></i>
                <div style="font-size:1.15rem;font-weight:600;margin-bottom:6px;">No surveys found</div>
                <div style="font-size:0.98rem;">Be the first to add a survey by clicking the <b>+</b> button below!</div>
            </div>
        @else
            @foreach($customers as $customer)
            <div class="survey-card" style="background:#fff;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.07);padding:1.2rem 1rem;margin-bottom:1.2rem;">
                <div style="font-weight:600;font-size:1.08rem;color:#333;">
                    {{ $customer->full_name }}
                    <span style="background:#e3f7ff;color:#007bff;font-size:0.85rem;padding:2px 10px;border-radius:8px;margin-left:8px;">{{ $customer->interest }}</span>
                </div>
                <div style="font-size:0.97rem;color:#666;margin:2px 0;">📞 {{ $customer->mobile }}</div>
                <div style="font-size:0.97rem;color:#666;margin:2px 0;">📍 {{ $customer->city }}, {{ $customer->address }}</div>
                <div style="font-size:0.97rem;color:#666;margin:2px 0;">🔋 {{ $customer->load_requirement }} KW • {{ $customer->service_type }}</div>
                <div style="font-size:0.97rem;color:#666;margin:2px 0;">🗓 Surveyed: {{ $customer->created_at->format('d M Y') }}</div>
                <div style="margin-top:10px;display:flex;gap:10px;">
                    <a href="{{ url('/survey/view/'.$customer->id) }}" class="btn btn-primary" style="flex:1;">View Details</a>
                    <a href="{{ url('/quote/form/'.$customer->id) }}" class="btn btn-secondary" style="flex:1;">Quote</a>
                </div>
            </div>
            @endforeach
        @endif
    </div>
@include('components.bottomtab', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
<style>
.survey-plus-btn:hover { background: #0056b3; box-shadow:0 6px 20px rgba(0,123,255,0.22); }
</style>
@endsection
