
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
.dashboard-header-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.7rem 1.2rem 0.7rem 1.2rem;
    background: #fff;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    margin-bottom: 0.5rem;
    position: relative;
}
.dashboard-logo { height: 32px; }
.dashboard-user {
    font-weight: 600;
    color: #007bff;
    font-size: 1.05rem;
    cursor: pointer;
    position: relative;
}
.account-dropdown {
    display: none;
    position: absolute;
    right: 0;
    top: 2.5rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    min-width: 140px;
    z-index: 10;
}
.account-dropdown.show { display: block; }
.account-dropdown a {
    display: block;
    padding: 0.7rem 1rem;
    color: #333;
    text-decoration: none;
    font-size: 0.98rem;
}
.account-dropdown a:hover { background: #f3f6fa; }
.account-dropdown .logout { color: #e74c3c; }
</style>
<div class="dashboard-header-bar">
    <img src="{{ asset('images/urjamart-logo.png') }}" class="dashboard-logo" alt="Logo">
    <div class="dashboard-user" onclick="toggleAccountDropdown()" style="font-weight:600;font-size:16px;margin-left:12px;">
        <i class="fa fa-user-circle"></i>
        {{ $Userdetails->name ?? 'User' }} <span style="color:#007bff; font-weight:500;">({{ ucfirst($usertype) }})</span>
        <i class="fa fa-chevron-down" style="margin-left:6px;font-size:0.8rem;"></i>
        <div id="accountDropdown" class="account-dropdown">
            <a href="#" onclick="showProfile()">
                <i class="fa fa-user" style="margin-right:8px;"></i>
                Profile
            </a>
            <a href="#" onclick="showSettings()">
                <i class="fa fa-cog" style="margin-right:8px;"></i>
                Settings
            </a>
            <a href="#" onclick="logout()" class="logout">
                <i class="fa fa-sign-out-alt" style="margin-right:8px;"></i>
                Logout
            </a>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function toggleAccountDropdown() {
    var dropdown = document.getElementById('accountDropdown');
    dropdown.classList.toggle('show');
}

function showProfile() {
    Swal.fire({
        icon: 'info',
        title: 'User Profile',
        html: `
            <div style="text-align:left;background:#f8f9fa;border-radius:8px;padding:1.5rem;margin:1rem 0;">
                <div style="display:flex;align-items:center;margin-bottom:1rem;">
                    <div style="width:60px;height:60px;background:linear-gradient(135deg,#007bff,#0056b3);border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.5rem;margin-right:1rem;">
                        <i class="fa fa-user"></i>
                    </div>
                    <div>
                        <h4 style="margin:0;color:#333;font-size:1.2rem;">{{ $Userdetails->name ?? 'User' }}</h4>
                        <p style="margin:0;color:#007bff;font-weight:600;">{{ ucfirst($usertype) }}</p>
                    </div>
                </div>

                <div style="border-top:1px solid #dee2e6;padding-top:1rem;">
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:0.5rem;">
                        <div><strong>User ID:</strong></div>
                        <div>{{ $Userdetails->id ?? 'N/A' }}</div>
                    </div>

                    @if(isset($Userdetails->email))
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:0.5rem;">
                        <div><strong>Email:</strong></div>
                        <div>{{ $Userdetails->email }}</div>
                    </div>
                    @endif

                    @if(isset($Userdetails->mobile))
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:0.5rem;">
                        <div><strong>Mobile:</strong></div>
                        <div>{{ $Userdetails->mobile }}</div>
                    </div>
                    @endif

                    @if(isset($Userdetails->city))
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:0.5rem;">
                        <div><strong>City:</strong></div>
                        <div>{{ $Userdetails->city }}</div>
                    </div>
                    @endif

                    @if(isset($Userdetails->address))
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:0.5rem;">
                        <div><strong>Address:</strong></div>
                        <div>{{ $Userdetails->address }}</div>
                    </div>
                    @endif

                    @if(isset($Userdetails->company_name))
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:0.5rem;">
                        <div><strong>Company:</strong></div>
                        <div>{{ $Userdetails->company_name }}</div>
                    </div>
                    @endif

                    @if(isset($Userdetails->designation))
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:0.5rem;">
                        <div><strong>Designation:</strong></div>
                        <div>{{ $Userdetails->designation }}</div>
                    </div>
                    @endif

                    @if(isset($Userdetails->experience))
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:0.5rem;">
                        <div><strong>Experience:</strong></div>
                        <div>{{ $Userdetails->experience }} years</div>
                    </div>
                    @endif

                    @if(isset($Userdetails->created_at))
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:0.5rem;">
                        <div><strong>Member Since:</strong></div>
                        <div>{{ $Userdetails->created_at->format('M Y') }}</div>
                    </div>
                    @endif
                </div>
            </div>
        `,
        confirmButtonText: 'Close',
        width: '500px'
    });
    document.getElementById('accountDropdown').classList.remove('show');
}

function showSettings() {
    Swal.fire({
        icon: 'info',
        title: 'Settings',
        text: 'Settings functionality coming soon!',
        confirmButtonText: 'OK'
    });
    document.getElementById('accountDropdown').classList.remove('show');
}

function logout() {
    Swal.fire({
        title: 'Logout',
        text: 'Are you sure you want to logout?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#e74c3c',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, Logout',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/logout';
        }
    });
    document.getElementById('accountDropdown').classList.remove('show');
}

document.addEventListener('click', function(e) {
    var dropdown = document.getElementById('accountDropdown');
    var userDiv = e.target.closest('.dashboard-user');
    if (dropdown && !dropdown.contains(e.target) && !userDiv) {
        dropdown.classList.remove('show');
    }
});
</script>
