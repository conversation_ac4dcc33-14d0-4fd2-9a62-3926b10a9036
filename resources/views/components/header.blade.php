
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
.dashboard-header-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.7rem 1.2rem 0.7rem 1.2rem;
    background: #fff;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    margin-bottom: 0.5rem;
    position: relative;
}
.dashboard-logo { height: 32px; }
.dashboard-user {
    font-weight: 600;
    color: #007bff;
    font-size: 1.05rem;
    cursor: pointer;
    position: relative;
}
.account-dropdown {
    display: none;
    position: absolute;
    right: 0;
    top: 2.5rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    min-width: 140px;
    z-index: 10;
}
.account-dropdown.show { display: block; }
.account-dropdown a {
    display: block;
    padding: 0.7rem 1rem;
    color: #333;
    text-decoration: none;
    font-size: 0.98rem;
}
.account-dropdown a:hover { background: #f3f6fa; }
.account-dropdown .logout { color: #e74c3c; }
</style>
<div class="dashboard-header-bar">
    <img src="{{ asset('images/urjamart-logo.png') }}" class="dashboard-logo" alt="Logo">
    <span class="dashboard-user" style="font-weight:600;font-size:16px;margin-left:12px;">
        <i class="fa fa-user-circle"></i>
        {{ $Userdetails->name ?? 'User' }} <span style="color:#007bff; font-weight:500;">({{ ucfirst($usertype) }})</span>
    </span>
</div>
<script>
document.addEventListener('click', function(e) {
    var dropdown = document.getElementById('accountDropdown');
    if (dropdown && !dropdown.contains(e.target) && !e.target.classList.contains('dashboard-user')) {
        dropdown.classList.remove('show');
    }
});
</script>
