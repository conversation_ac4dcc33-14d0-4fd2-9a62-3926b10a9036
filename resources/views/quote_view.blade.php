@extends('layouts.app')
@section('content')
@include('components.header', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
<div style="max-width:420px;margin:30px auto 70px auto;">
    <div style="background:#fff;border-radius:16px;box-shadow:0 2px 12px rgba(0,0,0,0.08);padding:1.5rem;">
        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:1.2rem;">
            <h3 style="font-size:1.15rem;font-weight:600;color:#333;margin:0;">Quotation Details</h3>
            <div style="background:{{ $quote->status == 'sent' ? '#e3f7ff' : ($quote->status == 'accepted' ? '#d4edda' : ($quote->status == 'rejected' ? '#f8d7da' : '#f8f9fa')) }};color:{{ $quote->status == 'sent' ? '#007bff' : ($quote->status == 'accepted' ? '#155724' : ($quote->status == 'rejected' ? '#721c24' : '#6c757d')) }};font-size:0.85rem;padding:4px 12px;border-radius:8px;">
                {{ ucfirst($quote->status) }}
            </div>
        </div>
        
        <div style="background:#f8f9fa;border-radius:8px;padding:1rem;margin-bottom:1.2rem;">
            <div style="font-weight:600;margin-bottom:0.5rem;">Quote: {{ $quote->quote_number }}</div>
            <div style="font-size:0.9rem;color:#666;">Created: {{ $quote->created_at->format('d M Y, h:i A') }}</div>
            <div style="font-size:0.9rem;color:#666;">Valid until: {{ $quote->valid_until->format('d M Y') }}</div>
        </div>

        <div style="margin-bottom:1.2rem;">
            <div style="font-weight:600;margin-bottom:0.5rem;">Customer Information</div>
            <div style="margin-bottom:0.5rem;"><b>Name:</b> {{ $quote->customer->full_name }}</div>
            <div style="margin-bottom:0.5rem;"><b>Mobile:</b> {{ $quote->customer->mobile }}</div>
            <div style="margin-bottom:0.5rem;"><b>Address:</b> {{ $quote->customer->address }}, {{ $quote->customer->city }}</div>
            <div style="margin-bottom:0.5rem;"><b>Load Requirement:</b> {{ $quote->customer->load_requirement }} KW</div>
        </div>

        <div style="margin-bottom:1.2rem;">
            <div style="font-weight:600;margin-bottom:0.5rem;">System Configuration</div>
            <div style="margin-bottom:0.5rem;"><b>System Capacity:</b> {{ $quote->system_capacity }} KW</div>
            <div style="margin-bottom:0.5rem;"><b>Panel Count:</b> {{ $quote->panel_count }} panels</div>
            <div style="margin-bottom:0.5rem;"><b>Panel Type:</b> {{ $quote->panel_type }}</div>
            <div style="margin-bottom:0.5rem;"><b>Panel Wattage:</b> {{ $quote->panel_wattage }} W</div>
            <div style="margin-bottom:0.5rem;"><b>Inverter Type:</b> {{ $quote->inverter_type }}</div>
            <div style="margin-bottom:0.5rem;"><b>Inverter Capacity:</b> {{ $quote->inverter_capacity }} KW</div>
            <div style="margin-bottom:0.5rem;"><b>Mounting Structure:</b> {{ $quote->mounting_structure }}</div>
        </div>

        <div style="margin-bottom:1.2rem;">
            <div style="font-weight:600;margin-bottom:0.5rem;">Cost Breakdown</div>
            <div style="background:#f8f9fa;border-radius:8px;padding:1rem;">
                <div style="display:flex;justify-content:space-between;margin-bottom:0.5rem;">
                    <span>System Cost:</span>
                    <span>₹{{ number_format($quote->system_cost, 2) }}</span>
                </div>
                <div style="display:flex;justify-content:space-between;margin-bottom:0.5rem;">
                    <span>Installation Cost:</span>
                    <span>₹{{ number_format($quote->installation_cost, 2) }}</span>
                </div>
                <div style="display:flex;justify-content:space-between;margin-bottom:0.5rem;border-top:1px solid #dee2e6;padding-top:0.5rem;">
                    <span><b>Total Cost:</b></span>
                    <span><b>₹{{ number_format($quote->total_cost, 2) }}</b></span>
                </div>
                @if($quote->subsidy_amount)
                <div style="display:flex;justify-content:space-between;margin-bottom:0.5rem;color:#28a745;">
                    <span>Subsidy Amount:</span>
                    <span>-₹{{ number_format($quote->subsidy_amount, 2) }}</span>
                </div>
                @endif
                <div style="display:flex;justify-content:space-between;border-top:2px solid #007bff;padding-top:0.5rem;font-size:1.1rem;">
                    <span><b>Final Amount:</b></span>
                    <span style="color:#007bff;"><b>₹{{ number_format($quote->final_cost, 2) }}</b></span>
                </div>
            </div>
        </div>

        @if($quote->terms_conditions)
        <div style="margin-bottom:1.2rem;">
            <div style="font-weight:600;margin-bottom:0.5rem;">Terms & Conditions</div>
            <div style="background:#f8f9fa;border-radius:8px;padding:1rem;font-size:0.9rem;">
                {{ $quote->terms_conditions }}
            </div>
        </div>
        @endif

        @if($quote->notes)
        <div style="margin-bottom:1.2rem;">
            <div style="font-weight:600;margin-bottom:0.5rem;">Notes</div>
            <div style="background:#f8f9fa;border-radius:8px;padding:1rem;font-size:0.9rem;">
                {{ $quote->notes }}
            </div>
        </div>
        @endif

        <div style="margin-bottom:1.2rem;">
            <div style="font-weight:600;margin-bottom:0.5rem;">Created By</div>
            <div style="font-size:0.9rem;color:#666;">{{ $quote->created_by_usertype }} (ID: {{ $quote->created_by_userid }})</div>
        </div>

        <div style="display:flex;gap:10px;">
            <a href="{{ url('/quotation') }}" class="btn btn-secondary" style="flex:1;">Back to List</a>
            <a href="{{ url('/quote/form/'.$quote->customer_id) }}" class="btn btn-primary" style="flex:1;">Edit Quote</a>
        </div>
    </div>
</div>
@include('components.bottomtab', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
@endsection
