@extends('layouts.app')

@section('content')

@include('components.header', ['usertype' => $usertype, 'Userdetails' => $userdetails])

<div class="dashboard-main" style="position:relative;">
    <div class="dashboard-welcome-box">
        <div style="font-size:1.15rem; font-weight:600; color:#333; margin-bottom:6px;">Welcome, {{ $userdetails->name ?? 'User' }}!</div>
        <div style="font-size:0.98rem; color:#666;">Ready to capture more leads today?</div>
    </div>
    <div class="dashboard-stats">
        <div class="dashboard-stat">
            <div class="dashboard-stat-value">{{ $totalSurveys }}</div>
            <div class="dashboard-stat-label">Total Surveys</div>
        </div>
        <div class="dashboard-stat">
            <div class="dashboard-stat-value" style="color:#28a745;">{{ $acceptedQuotes }}</div>
            <div class="dashboard-stat-label">Accepted Quotes</div>
        </div>
        <div class="dashboard-stat">
            <div class="dashboard-stat-value" style="color:#ffc107;">{{ $pendingQuotes }}</div>
            <div class="dashboard-stat-label">Pending Quotes</div>
        </div>
        <div class="dashboard-stat">
            <div class="dashboard-stat-value" style="color:#17a2b8;">{{ $totalQuotes }}</div>
            <div class="dashboard-stat-label">Total Quotes</div>
        </div>
    </div>
    <div style="font-size:1rem; font-weight:600; color:#888; margin-bottom:0.7rem;">QUICK ACTIONS</div>
    <div style="display:grid; grid-template-columns:1fr 1fr; gap:16px; margin-bottom:1.5rem;">
        <a href="{{ url('/survey') }}" class="dashboard-action-btn" style="background:#fff; border-radius:10px; box-shadow:0 1px 4px rgba(0,0,0,0.06); padding:1rem; text-align:center; cursor:pointer; text-decoration:none;">
            <div style="font-size:1.3rem; color:#007bff;"><i class="fa fa-file-alt"></i></div>
            <div style="font-weight:600; margin-top:4px;">Survey</div>
            <div style="font-size:0.95rem; color:#888;">Start a new survey</div>
        </a>
        <a href="{{ url('/quotation') }}" class="dashboard-action-btn" style="background:#fff; border-radius:10px; box-shadow:0 1px 4px rgba(0,0,0,0.06); padding:1rem; text-align:center; cursor:pointer; text-decoration:none;">
            <div style="font-size:1.3rem; color:#007bff;"><i class="fa fa-file-invoice"></i></div>
            <div style="font-weight:600; margin-top:4px;">Quotation</div>
            <div style="font-size:0.95rem; color:#888;">Generate quotation</div>
        </a>
    </div>

    @if($recentSurveys->count() > 0 || $recentQuotes->count() > 0)
    <div style="font-size:1rem; font-weight:600; color:#888; margin-bottom:0.7rem;">RECENT ACTIVITY</div>

    @if($recentSurveys->count() > 0)
    <div style="margin-bottom:1rem;">
        <div style="font-size:0.9rem; font-weight:600; color:#666; margin-bottom:0.5rem;">Recent Surveys</div>
        @foreach($recentSurveys as $survey)
        <div style="background:#f8f9fa; border-radius:8px; padding:0.8rem; margin-bottom:0.5rem; border-left:3px solid #007bff;">
            <div style="font-weight:600; font-size:0.95rem;">{{ $survey->full_name }}</div>
            <div style="font-size:0.85rem; color:#666;">{{ $survey->city }} • {{ $survey->load_requirement }} KW</div>
            <div style="font-size:0.8rem; color:#888;">{{ $survey->created_at->diffForHumans() }}</div>
        </div>
        @endforeach
    </div>
    @endif

    @if($recentQuotes->count() > 0)
    <div style="margin-bottom:1rem;">
        <div style="font-size:0.9rem; font-weight:600; color:#666; margin-bottom:0.5rem;">Recent Quotes</div>
        @foreach($recentQuotes as $quote)
        <div style="background:#f8f9fa; border-radius:8px; padding:0.8rem; margin-bottom:0.5rem; border-left:3px solid #28a745;">
            <div style="font-weight:600; font-size:0.95rem;">{{ $quote->customer->full_name }}</div>
            <div style="font-size:0.85rem; color:#666;">₹{{ number_format($quote->final_cost, 0) }} • {{ $quote->system_capacity }} KW</div>
            <div style="font-size:0.8rem; color:#888;">{{ $quote->created_at->diffForHumans() }} • {{ ucfirst($quote->status) }}</div>
        </div>
        @endforeach
    </div>
    @endif
    @endif
</div>
<style>
.dashboard-main { max-width: 420px; margin: 30px auto 70px auto; background: #fff; border-radius: 16px; box-shadow: 0 2px 12px rgba(0,0,0,0.08); padding: 0 1.5rem 1.5rem 1.5rem; font-family: 'Segoe UI', Arial, sans-serif; }
.dashboard-welcome-box { background: #f3f6fa; border-radius: 10px; padding: 1.2rem 1rem; margin: 1.2rem 0 1.5rem 0; font-size: 1.1rem; color: #333; text-align: left; font-weight: 500; }
.dashboard-stats { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 1.5rem; }
.dashboard-stat { background: #fff; border-radius: 10px; box-shadow: 0 1px 4px rgba(0,0,0,0.06); padding: 0.7rem 0.5rem; text-align: center; }
.dashboard-stat-value { font-size: 1.2rem; font-weight: 700; color: #007bff; }
.dashboard-stat-label { font-size: 0.93rem; color: #888; }
.dashboard-action-btn { transition: box-shadow 0.2s; }
.dashboard-action-btn:hover { box-shadow: 0 2px 8px rgba(0,123,255,0.15); }
.dashboard-plus-btn:hover { background: #0056b3; }
</style>
@include('components.bottomtab', ['usertype' => $usertype, 'Userdetails' => $userdetails])
@endsection
