@extends('layouts.app')
@section('content')
@include('components.header', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
<div style="max-width:900px;margin:20px auto 70px auto;">
    <form method="POST" action="{{ url('/quote/submit') }}" style="background:#fff;border-radius:16px;box-shadow:0 2px 12px rgba(0,0,0,0.08);padding:2rem;">
        @csrf
        <h3 style="font-size:1.4rem;font-weight:600;color:#333;margin-bottom:2rem;text-align:center;border-bottom:2px solid #007bff;padding-bottom:1rem;">Quotation Management</h3>

        <!-- Customer Selection Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">Customer Selection</h4>
            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Select Customer <span style="color:red">*</span></label>
                <select name="customer_id" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;font-size:1rem;">
                    <option value="">Choose customer</option>
                    @foreach($customers as $cust)
                        <option value="{{ $cust->id }}" {{ $selected_customer == $cust->id ? 'selected' : '' }}>
                            {{ $cust->full_name }} - {{ $cust->mobile }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Lead Status</label>
                <select name="lead_status" class="form-control" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;font-size:1rem;">
                    <option value="Site Inspection Completed">Site Inspection Completed</option>
                    <option value="Proposal Sent">Proposal Sent</option>
                    <option value="Follow Up Required">Follow Up Required</option>
                    <option value="Ready to Close">Ready to Close</option>
                </select>
            </div>
        </div>

        @if($customer)
        <div style="background:#e3f7ff;border-radius:8px;padding:1rem;margin-bottom:1.5rem;border-left:4px solid #007bff;">
            <div style="font-weight:600;margin-bottom:0.5rem;color:#007bff;">Customer Details:</div>
            <div style="font-size:0.9rem;color:#666;">
                <div>📍 {{ $customer->address }}, {{ $customer->city }}</div>
                <div>🔋 Load: {{ $customer->load_requirement }} KW</div>
                <div>🏠 {{ $customer->roof_type }} • {{ $customer->service_type }}</div>
            </div>
        </div>
        @endif

        <!-- System Configuration Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">System Configuration</h4>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">System Type <span style="color:red">*</span></label>
                    <select name="system_type" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="">Select system type</option>
                        <option value="On-Grid">On-Grid</option>
                        <option value="Off-Grid">Off-Grid</option>
                        <option value="Hybrid">Hybrid</option>
                    </select>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">System Capacity (KW) <span style="color:red">*</span></label>
                    <input type="number" name="system_capacity" class="form-control" step="0.01" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                </div>
            </div>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Panel Type <span style="color:red">*</span></label>
                    <select name="panel_type" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="">Select panel type</option>
                        <option value="Monocrystalline">Monocrystalline</option>
                        <option value="Polycrystalline">Polycrystalline</option>
                        <option value="Bifacial">Bifacial</option>
                    </select>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Inverter Type <span style="color:red">*</span></label>
                    <select name="inverter_type" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="">Select inverter type</option>
                        <option value="String Inverter">String Inverter</option>
                        <option value="Power Optimizer">Power Optimizer</option>
                        <option value="Micro Inverter">Micro Inverter</option>
                    </select>
                </div>
            </div>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Mounting Type <span style="color:red">*</span></label>
                    <select name="mounting_type" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="">Select mounting type</option>
                        <option value="Roof Mount">Roof Mount</option>
                        <option value="Ground Mount">Ground Mount</option>
                        <option value="Pole Mount">Pole Mount</option>
                    </select>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Battery (if applicable)</label>
                    <select name="battery_type" class="form-control" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="">No Battery</option>
                        <option value="Lithium Ion">Lithium Ion</option>
                        <option value="Lead Acid">Lead Acid</option>
                        <option value="Gel Battery">Gel Battery</option>
                    </select>
                </div>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">
                    <input type="checkbox" name="expert_rfi_mounting" value="1" style="margin-right:0.5rem;">
                    Expert RFI Mounting
                </label>
            </div>
        </div>
        <!-- Cost Breakdown Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">Cost Breakdown</h4>

            <!-- Cost Table -->
            <div style="background:#fff;border-radius:8px;overflow:hidden;border:1px solid #ddd;margin-bottom:1rem;">
                <table style="width:100%;border-collapse:collapse;">
                    <thead>
                        <tr style="background:#007bff;color:#fff;">
                            <th style="padding:0.75rem;text-align:left;border-right:1px solid #0056b3;">Component</th>
                            <th style="padding:0.75rem;text-align:left;border-right:1px solid #0056b3;">Specification</th>
                            <th style="padding:0.75rem;text-align:center;border-right:1px solid #0056b3;">Quantity</th>
                            <th style="padding:0.75rem;text-align:right;border-right:1px solid #0056b3;">Unit Price (₹)</th>
                            <th style="padding:0.75rem;text-align:right;">Total (₹)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom:1px solid #eee;">
                            <td style="padding:0.75rem;border-right:1px solid #eee;">Solar Panel</td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;">
                                <input type="text" name="panel_specification" placeholder="e.g., 540W Mono PERC" style="border:none;width:100%;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:center;">
                                <input type="number" name="panel_count" required style="border:none;width:60px;text-align:center;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:right;">
                                <input type="number" name="panel_unit_price" step="0.01" style="border:none;width:80px;text-align:right;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="panel_total_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;" readonly>
                            </td>
                        </tr>
                        <tr style="border-bottom:1px solid #eee;">
                            <td style="padding:0.75rem;border-right:1px solid #eee;">Inverter</td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;">
                                <input type="text" name="inverter_specification" placeholder="e.g., 5KW String Inverter" style="border:none;width:100%;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:center;">
                                <input type="number" name="inverter_quantity" value="1" style="border:none;width:60px;text-align:center;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:right;">
                                <input type="number" name="inverter_unit_price" step="0.01" style="border:none;width:80px;text-align:right;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="inverter_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;" readonly>
                            </td>
                        </tr>
                        <tr style="border-bottom:1px solid #eee;">
                            <td style="padding:0.75rem;border-right:1px solid #eee;">Mounting Structure</td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;">
                                <input type="text" name="mounting_specification" placeholder="e.g., Galvanized Steel" style="border:none;width:100%;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:center;">
                                <input type="number" name="mounting_quantity" value="1" style="border:none;width:60px;text-align:center;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:right;">
                                <input type="number" name="mounting_unit_price" step="0.01" style="border:none;width:80px;text-align:right;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="mounting_structure_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;" readonly>
                            </td>
                        </tr>
                        <tr style="border-bottom:1px solid #eee;">
                            <td style="padding:0.75rem;border-right:1px solid #eee;">Transformers & Labor</td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;">
                                <input type="text" name="labor_specification" placeholder="Complete Installation" style="border:none;width:100%;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:center;">
                                <input type="number" name="labor_quantity" value="1" style="border:none;width:60px;text-align:center;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:right;">
                                <input type="number" name="labor_unit_price" step="0.01" style="border:none;width:80px;text-align:right;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="transformers_labor_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;" readonly>
                            </td>
                        </tr>
                        <tr style="border-bottom:1px solid #eee;">
                            <td style="padding:0.75rem;border-right:1px solid #eee;">Permits & Documentation</td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;">
                                <input type="text" name="permits_specification" placeholder="All Required Permits" style="border:none;width:100%;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:center;">
                                <input type="number" name="permits_quantity" value="1" style="border:none;width:60px;text-align:center;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:right;">
                                <input type="number" name="permits_unit_price" step="0.01" style="border:none;width:80px;text-align:right;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="permits_documentation_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;" readonly>
                            </td>
                        </tr>
                        <tr style="background:#f8f9fa;font-weight:600;">
                            <td colspan="4" style="padding:0.75rem;text-align:right;border-right:1px solid #ddd;">Subtotal:</td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="subtotal" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;font-weight:600;" readonly>
                            </td>
                        </tr>
                        <tr style="background:#fff3cd;">
                            <td colspan="4" style="padding:0.75rem;text-align:right;border-right:1px solid #ddd;">Subsidy:</td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="subsidy_amount" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#fff3cd;">
                            </td>
                        </tr>
                        <tr style="background:#d4edda;">
                            <td colspan="4" style="padding:0.75rem;text-align:right;border-right:1px solid #ddd;font-weight:600;">GST (18%):</td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="gst_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#d4edda;font-weight:600;" readonly>
                            </td>
                        </tr>
                        <tr style="background:#007bff;color:#fff;font-weight:600;font-size:1.1rem;">
                            <td colspan="4" style="padding:1rem;text-align:right;border-right:1px solid #0056b3;">Total Amount:</td>
                            <td style="padding:1rem;text-align:right;">
                                <input type="number" name="total_cost" step="0.01" style="border:none;width:120px;text-align:right;padding:0.5rem;background:#007bff;color:#fff;font-weight:600;font-size:1.1rem;" readonly>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!-- Financial Information Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">Financial Information</h4>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Financing Options</label>
                    <select name="financing_option" class="form-control" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="Full Payment">Full Payment</option>
                        <option value="EMI Available">EMI Available</option>
                        <option value="Lease Option">Lease Option</option>
                    </select>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Government Subsidy</label>
                    <input type="number" name="government_subsidy" step="0.01" placeholder="Available subsidy amount" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                </div>
            </div>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Net Amount After Subsidy</label>
                    <input type="number" name="net_amount_after_subsidy" step="0.01" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;background:#f8f9fa;" readonly>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Estimated Payback Period (Years)</label>
                    <input type="number" name="estimated_payback_period" step="0.1" placeholder="4.5" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                </div>
            </div>
        </div>

        <!-- Savings Information Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">Savings on Investment</h4>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Monthly Energy Generation (kWh)</label>
                    <input type="number" name="monthly_energy_generation_kwh" step="0.01" placeholder="500" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Monthly Savings (₹)</label>
                    <input type="number" name="monthly_savings" step="0.01" placeholder="3500" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                </div>
            </div>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Annual Savings (₹)</label>
                    <input type="number" name="annual_savings" step="0.01" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;background:#f8f9fa;" readonly>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">25 Year Total Savings (₹)</label>
                    <input type="number" name="year_total_savings" step="0.01" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;background:#f8f9fa;" readonly>
                </div>
            </div>
        </div>

        <!-- Terms & Conditions Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">Terms & Conditions</h4>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Warranty Details</label>
                <textarea name="warranty_details" class="form-control" rows="2" placeholder="25 year performance warranty, 10 year product warranty..." style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;resize:vertical;"></textarea>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Installation Timeline</label>
                <textarea name="installation_timeline" class="form-control" rows="2" placeholder="30 days from approval, 5 year maintenance..." style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;resize:vertical;"></textarea>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Payment Terms</label>
                <textarea name="payment_terms_detailed" class="form-control" rows="2" placeholder="30% advance, 70% on completion..." style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;resize:vertical;"></textarea>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Validity</label>
                <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;">
                    <input type="date" name="valid_until" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                    <textarea name="validity_details" class="form-control" rows="1" placeholder="30 days validity" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;resize:vertical;"></textarea>
                </div>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Additional Terms</label>
                <textarea name="additional_terms" class="form-control" rows="3" placeholder="Any additional terms, conditions, or notes..." style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;resize:vertical;"></textarea>
            </div>
        </div>

        <div style="text-align:center;">
            <button type="submit" class="btn btn-warning" style="font-weight:600;font-size:1.1rem;padding:1rem 3rem;border-radius:10px;background:linear-gradient(135deg,#ffc107 0%,#ff8c00 100%);border:none;color:#fff;box-shadow:0 4px 15px rgba(255,193,7,0.3);">
                <i class="fa fa-save" style="margin-right:0.5rem;"></i>
                Create Quotation
            </button>
        </div>
    </form>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
    // Auto-calculation functions
    function calculateRowTotal(row) {
        const quantity = parseFloat(row.querySelector('input[name*="quantity"], input[name*="count"]').value) || 0;
        const unitPrice = parseFloat(row.querySelector('input[name*="unit_price"]').value) || 0;
        const totalField = row.querySelector('input[name*="cost"], input[name*="total_cost"]');
        const total = quantity * unitPrice;
        totalField.value = total.toFixed(2);
        calculateSubtotal();
    }

    function calculateSubtotal() {
        const costFields = [
            'panel_total_cost', 'inverter_cost', 'mounting_structure_cost',
            'transformers_labor_cost', 'permits_documentation_cost'
        ];
        let subtotal = 0;

        costFields.forEach(field => {
            const value = parseFloat(document.querySelector(`input[name="${field}"]`).value) || 0;
            subtotal += value;
        });

        document.querySelector('input[name="subtotal"]').value = subtotal.toFixed(2);
        calculateGSTAndTotal();
    }

    function calculateGSTAndTotal() {
        const subtotal = parseFloat(document.querySelector('input[name="subtotal"]').value) || 0;
        const subsidy = parseFloat(document.querySelector('input[name="subsidy_amount"]').value) || 0;
        const netAmount = subtotal - subsidy;
        const gst = netAmount * 0.18;
        const total = netAmount + gst;

        document.querySelector('input[name="gst_cost"]').value = gst.toFixed(2);
        document.querySelector('input[name="total_cost"]').value = total.toFixed(2);

        // Update financial calculations
        updateFinancialCalculations(total);
    }

    function updateFinancialCalculations(totalCost) {
        const govSubsidy = parseFloat(document.querySelector('input[name="government_subsidy"]').value) || 0;
        const netAfterSubsidy = totalCost - govSubsidy;
        document.querySelector('input[name="net_amount_after_subsidy"]').value = netAfterSubsidy.toFixed(2);

        // Calculate savings
        const monthlySavings = parseFloat(document.querySelector('input[name="monthly_savings"]').value) || 0;
        const annualSavings = monthlySavings * 12;
        const totalSavings25Years = annualSavings * 25;

        document.querySelector('input[name="annual_savings"]').value = annualSavings.toFixed(2);
        document.querySelector('input[name="year_total_savings"]').value = totalSavings25Years.toFixed(2);
    }

    // Add event listeners for auto-calculation
    document.addEventListener('DOMContentLoaded', function() {
        // Cost calculation listeners
        const costInputs = document.querySelectorAll('input[name*="quantity"], input[name*="count"], input[name*="unit_price"]');
        costInputs.forEach(input => {
            input.addEventListener('input', function() {
                const row = this.closest('tr');
                if (row) calculateRowTotal(row);
            });
        });

        // Subsidy change listener
        document.querySelector('input[name="subsidy_amount"]').addEventListener('input', calculateGSTAndTotal);
        document.querySelector('input[name="government_subsidy"]').addEventListener('input', function() {
            const totalCost = parseFloat(document.querySelector('input[name="total_cost"]').value) || 0;
            updateFinancialCalculations(totalCost);
        });

        // Monthly savings listener
        document.querySelector('input[name="monthly_savings"]').addEventListener('input', function() {
            const totalCost = parseFloat(document.querySelector('input[name="total_cost"]').value) || 0;
            updateFinancialCalculations(totalCost);
        });
    });

    // Form submission
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();
        var form = this;
        var formData = new FormData(form);

        Swal.fire({
            title: 'Creating Quotation...',
            allowOutsideClick: false,
            didOpen: () => { Swal.showLoading(); }
        });

        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(async response => {
            let data;
            try { data = await response.json(); } catch (e) { data = {}; }
            if (response.ok && data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Quotation Created!',
                    text: 'Your comprehensive quotation has been saved successfully.',
                    timer: 2000,
                    showConfirmButton: false,
                    willClose: () => { window.location.href = data.redirect; }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: (data.message || 'An error occurred. Please try again.'),
                    confirmButtonText: 'OK'
                });
            }
        });
    });
    </script>
</div>
@include('components.bottomtab', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
@endsection
