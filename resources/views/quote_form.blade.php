@extends('layouts.app')
@section('content')
@include('components.header', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
<div style="max-width:900px;margin:20px auto 70px auto;">
    <form method="POST" action="{{ url('/quote/submit') }}" style="background:#fff;border-radius:16px;box-shadow:0 2px 12px rgba(0,0,0,0.08);padding:2rem;">
        @csrf
        <h3 style="font-size:1.4rem;font-weight:600;color:#333;margin-bottom:2rem;text-align:center;border-bottom:2px solid #007bff;padding-bottom:1rem;">Quotation Management</h3>

        <!-- Customer Selection Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">Customer Selection</h4>
            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Select Customer <span style="color:red">*</span></label>
                <select name="customer_id" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;font-size:1rem;">
                    <option value="">Choose customer</option>
                    @foreach($customers as $cust)
                        <option value="{{ $cust->id }}" {{ $selected_customer == $cust->id ? 'selected' : '' }}>
                            {{ $cust->full_name }} - {{ $cust->mobile }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Lead Status</label>
                <select name="lead_status" class="form-control" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;font-size:1rem;">
                    <option value="Site Inspection Completed">Site Inspection Completed</option>
                    <option value="Proposal Sent">Proposal Sent</option>
                    <option value="Follow Up Required">Follow Up Required</option>
                    <option value="Ready to Close">Ready to Close</option>
                </select>
            </div>
        </div>

        @if($customer)
        <div style="background:#e3f7ff;border-radius:8px;padding:1rem;margin-bottom:1.5rem;border-left:4px solid #007bff;">
            <div style="font-weight:600;margin-bottom:0.5rem;color:#007bff;">Customer Details:</div>
            <div style="font-size:0.9rem;color:#666;">
                <div>📍 {{ $customer->address }}, {{ $customer->city }}</div>
                <div>🔋 Load: {{ $customer->load_requirement }} KW</div>
                <div>🏠 {{ $customer->roof_type }} • {{ $customer->service_type }}</div>
            </div>
        </div>
        @endif

        <!-- System Configuration Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">System Configuration</h4>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">System Type <span style="color:red">*</span></label>
                    <select name="system_type" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="">Select system type</option>
                        <option value="On-Grid">On-Grid</option>
                        <option value="Off-Grid">Off-Grid</option>
                        <option value="Hybrid">Hybrid</option>
                    </select>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">System Capacity (KW) <span style="color:red">*</span></label>
                    <input type="number" name="system_capacity" class="form-control" step="0.01" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                </div>
            </div>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Panel Type <span style="color:red">*</span></label>
                    <select name="panel_type" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="">Select panel type</option>
                        <option value="Monocrystalline">Monocrystalline</option>
                        <option value="Polycrystalline">Polycrystalline</option>
                        <option value="Bifacial">Bifacial</option>
                    </select>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Inverter Type <span style="color:red">*</span></label>
                    <select name="inverter_type" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="">Select inverter type</option>
                        <option value="String Inverter">String Inverter</option>
                        <option value="Power Optimizer">Power Optimizer</option>
                        <option value="Micro Inverter">Micro Inverter</option>
                    </select>
                </div>
            </div>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Mounting Type <span style="color:red">*</span></label>
                    <select name="mounting_type" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="">Select mounting type</option>
                        <option value="Roof Mount">Roof Mount</option>
                        <option value="Ground Mount">Ground Mount</option>
                        <option value="Pole Mount">Pole Mount</option>
                    </select>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Battery (if applicable)</label>
                    <select name="battery_type" class="form-control" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="">No Battery</option>
                        <option value="Lithium Ion">Lithium Ion</option>
                        <option value="Lead Acid">Lead Acid</option>
                        <option value="Gel Battery">Gel Battery</option>
                    </select>
                </div>
            </div>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Panel Wattage (W) <span style="color:red">*</span></label>
                    <input type="number" name="panel_wattage" class="form-control" step="1" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;" placeholder="540">
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Inverter Capacity (KW) <span style="color:red">*</span></label>
                    <input type="number" name="inverter_capacity" class="form-control" step="0.01" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;" placeholder="5.0">
                </div>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">
                    <input type="checkbox" name="expert_rfi_mounting" value="1" style="margin-right:0.5rem;">
                    Expert RFI Mounting
                </label>
            </div>
        </div>
        <!-- Cost Breakdown Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">Cost Breakdown</h4>

            <!-- Cost Table -->
            <div style="background:#fff;border-radius:8px;overflow-x:auto;border:1px solid #ddd;margin-bottom:1rem;max-width:100%;">
                <table style="width:100%;min-width:700px;border-collapse:collapse;">
                    <thead>
                        <tr style="background:#007bff;color:#fff;">
                            <th style="padding:0.75rem;text-align:left;border-right:1px solid #0056b3;">Component</th>
                            <th style="padding:0.75rem;text-align:left;border-right:1px solid #0056b3;">Specification</th>
                            <th style="padding:0.75rem;text-align:center;border-right:1px solid #0056b3;">Quantity</th>
                            <th style="padding:0.75rem;text-align:right;border-right:1px solid #0056b3;">Unit Price (₹)</th>
                            <th style="padding:0.75rem;text-align:right;">Total (₹)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom:1px solid #eee;">
                            <td style="padding:0.75rem;border-right:1px solid #eee;">Solar Panel</td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;">
                                <span id="panel_spec_display">540W Mono PERC</span>
                                <input type="hidden" name="panel_specification" value="540W Mono PERC">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:center;">
                                <input type="number" name="panel_count" required style="border:none;width:60px;text-align:center;padding:0.25rem;" value="10">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:right;">
                                <span>₹25,000</span>
                                <input type="hidden" name="panel_unit_price" value="25000">
                            </td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="panel_total_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;" readonly value="250000">
                            </td>
                        </tr>
                        <tr style="border-bottom:1px solid #eee;">
                            <td style="padding:0.75rem;border-right:1px solid #eee;">Inverter</td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;">
                                <span id="inverter_spec_display">5KW String Inverter</span>
                                <input type="hidden" name="inverter_specification" value="5KW String Inverter">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:center;">
                                <input type="number" name="inverter_quantity" value="1" style="border:none;width:60px;text-align:center;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:right;">
                                <span>₹45,000</span>
                                <input type="hidden" name="inverter_unit_price" value="45000">
                            </td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="inverter_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;" readonly value="45000">
                            </td>
                        </tr>
                        <tr style="border-bottom:1px solid #eee;">
                            <td style="padding:0.75rem;border-right:1px solid #eee;">Mounting Structure</td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;">
                                <span>Galvanized Steel</span>
                                <input type="hidden" name="mounting_specification" value="Galvanized Steel">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:center;">
                                <input type="number" name="mounting_quantity" value="1" style="border:none;width:60px;text-align:center;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:right;">
                                <span>₹35,000</span>
                                <input type="hidden" name="mounting_unit_price" value="35000">
                            </td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="mounting_structure_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;" readonly value="35000">
                            </td>
                        </tr>
                        <tr style="border-bottom:1px solid #eee;">
                            <td style="padding:0.75rem;border-right:1px solid #eee;">Transformers & Labor</td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;">
                                <span>Complete Installation</span>
                                <input type="hidden" name="labor_specification" value="Complete Installation">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:center;">
                                <input type="number" name="labor_quantity" value="1" style="border:none;width:60px;text-align:center;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:right;">
                                <span>₹25,000</span>
                                <input type="hidden" name="labor_unit_price" value="25000">
                            </td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="transformers_labor_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;" readonly value="25000">
                            </td>
                        </tr>
                        <tr style="border-bottom:1px solid #eee;">
                            <td style="padding:0.75rem;border-right:1px solid #eee;">Permits & Documentation</td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;">
                                <span>All Required Permits</span>
                                <input type="hidden" name="permits_specification" value="All Required Permits">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:center;">
                                <input type="number" name="permits_quantity" value="1" style="border:none;width:60px;text-align:center;padding:0.25rem;">
                            </td>
                            <td style="padding:0.75rem;border-right:1px solid #eee;text-align:right;">
                                <span>₹15,000</span>
                                <input type="hidden" name="permits_unit_price" value="15000">
                            </td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="permits_documentation_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;" readonly value="15000">
                            </td>
                        </tr>
                        <tr style="background:#f8f9fa;font-weight:600;">
                            <td colspan="4" style="padding:0.75rem;text-align:right;border-right:1px solid #ddd;">Subtotal:</td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="subtotal" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#f8f9fa;font-weight:600;" readonly value="370000">
                            </td>
                        </tr>
                        <tr style="background:#fff3cd;">
                            <td colspan="4" style="padding:0.75rem;text-align:right;border-right:1px solid #ddd;">Subsidy:</td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="subsidy_amount" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#fff3cd;" value="50000">
                            </td>
                        </tr>
                        <tr style="background:#d4edda;">
                            <td colspan="4" style="padding:0.75rem;text-align:right;border-right:1px solid #ddd;font-weight:600;">GST (18%):</td>
                            <td style="padding:0.75rem;text-align:right;">
                                <input type="number" name="gst_cost" step="0.01" style="border:none;width:100px;text-align:right;padding:0.25rem;background:#d4edda;font-weight:600;" readonly value="57600">
                            </td>
                        </tr>
                        <tr style="background:#007bff;color:#fff;font-weight:600;font-size:1.1rem;">
                            <td colspan="4" style="padding:1rem;text-align:right;border-right:1px solid #0056b3;">Total Amount:</td>
                            <td style="padding:1rem;text-align:right;">
                                <input type="number" name="total_cost" step="0.01" style="border:none;width:120px;text-align:right;padding:0.5rem;background:#007bff;color:#fff;font-weight:600;font-size:1.1rem;" readonly value="377600">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!-- Financial Information Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">Financial Information</h4>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Financing Options</label>
                    <select name="financing_option" class="form-control" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                        <option value="Full Payment">Full Payment</option>
                        <option value="EMI Available">EMI Available</option>
                        <option value="Lease Option">Lease Option</option>
                    </select>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Government Subsidy</label>
                    <input type="number" name="government_subsidy" step="0.01" placeholder="Available subsidy amount" value="78000" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                </div>
            </div>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Net Amount After Subsidy</label>
                    <input type="number" name="net_amount_after_subsidy" step="0.01" value="299600" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;background:#f8f9fa;" readonly>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Estimated Payback Period (Years)</label>
                    <input type="number" name="estimated_payback_period" step="0.1" value="4.5" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                </div>
            </div>
        </div>

        <!-- Savings Information Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">Savings on Investment</h4>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Monthly Energy Generation (kWh)</label>
                    <input type="number" name="monthly_energy_generation_kwh" step="0.01" value="650" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Monthly Savings (₹)</label>
                    <input type="number" name="monthly_savings" step="0.01" value="4500" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;">
                </div>
            </div>

            <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1rem;">
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Annual Savings (₹)</label>
                    <input type="number" name="annual_savings" step="0.01" value="54000" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;background:#f8f9fa;" readonly>
                </div>
                <div>
                    <label style="font-weight:600;margin-bottom:0.5rem;display:block;">25 Year Total Savings (₹)</label>
                    <input type="number" name="year_total_savings" step="0.01" value="1350000" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;background:#f8f9fa;" readonly>
                </div>
            </div>
        </div>

        <!-- Terms & Conditions Section -->
        <div style="background:#f8f9fa;border-radius:12px;padding:1.5rem;margin-bottom:2rem;">
            <h4 style="font-size:1.1rem;font-weight:600;color:#007bff;margin-bottom:1rem;">Terms & Conditions</h4>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Warranty Details</label>
                <textarea name="warranty_details" class="form-control" rows="2" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;resize:vertical;">25 year performance warranty on panels, 10 year product warranty, 5 year comprehensive warranty on inverter and installation</textarea>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Installation Timeline</label>
                <textarea name="installation_timeline" class="form-control" rows="2" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;resize:vertical;">Installation within 30 days from approval, 5 year maintenance support included, Net metering setup assistance</textarea>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Payment Terms</label>
                <textarea name="payment_terms_detailed" class="form-control" rows="2" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;resize:vertical;">30% advance payment, 40% on material delivery, 30% on completion and commissioning</textarea>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Validity</label>
                <div style="display:grid;grid-template-columns:1fr 1fr;gap:1rem;">
                    <input type="date" name="valid_until" class="form-control" required style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;" value="{{ date('Y-m-d', strtotime('+30 days')) }}">
                    <textarea name="validity_details" class="form-control" rows="1" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;resize:vertical;">Quote valid for 30 days from date of issue</textarea>
                </div>
            </div>

            <div style="margin-bottom:1rem;">
                <label style="font-weight:600;margin-bottom:0.5rem;display:block;">Additional Terms</label>
                <textarea name="additional_terms" class="form-control" rows="3" style="padding:0.75rem;border:1px solid #ddd;border-radius:8px;width:100%;resize:vertical;">All prices are inclusive of GST. Site conditions may affect final pricing. Government subsidy subject to availability and eligibility. Installation subject to structural feasibility.</textarea>
            </div>
        </div>

        <div style="text-align:center;">
            <button type="submit" class="btn btn-warning" style="font-weight:600;font-size:1.1rem;padding:1rem 3rem;border-radius:10px;background:linear-gradient(135deg,#ffc107 0%,#ff8c00 100%);border:none;color:#fff;box-shadow:0 4px 15px rgba(255,193,7,0.3);">
                <i class="fa fa-save" style="margin-right:0.5rem;"></i>
                Create Quotation
            </button>
        </div>
    </form>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
    // Auto-calculation functions with static unit prices
    const unitPrices = {
        'panel_count': 25000,
        'inverter_quantity': 45000,
        'mounting_quantity': 35000,
        'labor_quantity': 25000,
        'permits_quantity': 15000
    };

    function calculateRowTotal(input) {
        const inputName = input.name;
        const quantity = parseFloat(input.value) || 0;
        const unitPrice = unitPrices[inputName] || 0;

        let totalFieldName = '';
        if (inputName === 'panel_count') totalFieldName = 'panel_total_cost';
        else if (inputName === 'inverter_quantity') totalFieldName = 'inverter_cost';
        else if (inputName === 'mounting_quantity') totalFieldName = 'mounting_structure_cost';
        else if (inputName === 'labor_quantity') totalFieldName = 'transformers_labor_cost';
        else if (inputName === 'permits_quantity') totalFieldName = 'permits_documentation_cost';

        if (totalFieldName) {
            const totalField = document.querySelector(`input[name="${totalFieldName}"]`);
            const total = quantity * unitPrice;
            totalField.value = total.toFixed(0);
            calculateSubtotal();
        }
    }

    function calculateSubtotal() {
        const costFields = [
            'panel_total_cost', 'inverter_cost', 'mounting_structure_cost',
            'transformers_labor_cost', 'permits_documentation_cost'
        ];
        let subtotal = 0;

        costFields.forEach(field => {
            const value = parseFloat(document.querySelector(`input[name="${field}"]`).value) || 0;
            subtotal += value;
        });

        document.querySelector('input[name="subtotal"]').value = subtotal.toFixed(2);
        calculateGSTAndTotal();
    }

    function calculateGSTAndTotal() {
        const subtotal = parseFloat(document.querySelector('input[name="subtotal"]').value) || 0;
        const subsidy = parseFloat(document.querySelector('input[name="subsidy_amount"]').value) || 0;
        const netAmount = subtotal - subsidy;
        const gst = netAmount * 0.18;
        const total = netAmount + gst;

        document.querySelector('input[name="gst_cost"]').value = gst.toFixed(2);
        document.querySelector('input[name="total_cost"]').value = total.toFixed(2);

        // Update financial calculations
        updateFinancialCalculations(total);
    }

    function updateFinancialCalculations(totalCost) {
        const govSubsidy = parseFloat(document.querySelector('input[name="government_subsidy"]').value) || 0;
        const netAfterSubsidy = totalCost - govSubsidy;
        document.querySelector('input[name="net_amount_after_subsidy"]').value = netAfterSubsidy.toFixed(2);

        // Calculate savings
        const monthlySavings = parseFloat(document.querySelector('input[name="monthly_savings"]').value) || 0;
        const annualSavings = monthlySavings * 12;
        const totalSavings25Years = annualSavings * 25;

        document.querySelector('input[name="annual_savings"]').value = annualSavings.toFixed(2);
        document.querySelector('input[name="year_total_savings"]').value = totalSavings25Years.toFixed(2);
    }

    // Add event listeners for auto-calculation
    document.addEventListener('DOMContentLoaded', function() {
        // Cost calculation listeners for quantity inputs
        const quantityInputs = document.querySelectorAll('input[name="panel_count"], input[name="inverter_quantity"], input[name="mounting_quantity"], input[name="labor_quantity"], input[name="permits_quantity"]');
        quantityInputs.forEach(input => {
            input.addEventListener('input', function() {
                calculateRowTotal(this);
            });
        });

        // Initial calculation
        calculateSubtotal();

        // Panel wattage change listener to update specification
        document.querySelector('input[name="panel_wattage"]').addEventListener('input', function() {
            const wattage = this.value;
            const specDisplay = document.getElementById('panel_spec_display');
            const specInput = document.querySelector('input[name="panel_specification"]');
            const newSpec = wattage ? `${wattage}W Mono PERC` : '540W Mono PERC';
            specDisplay.textContent = newSpec;
            specInput.value = newSpec;
        });

        // Inverter capacity change listener to update specification
        document.querySelector('input[name="inverter_capacity"]').addEventListener('input', function() {
            const capacity = this.value;
            const specDisplay = document.getElementById('inverter_spec_display');
            const specInput = document.querySelector('input[name="inverter_specification"]');
            const newSpec = capacity ? `${capacity}KW String Inverter` : '5KW String Inverter';
            specDisplay.textContent = newSpec;
            specInput.value = newSpec;
        });

        // Subsidy change listener
        document.querySelector('input[name="subsidy_amount"]').addEventListener('input', calculateGSTAndTotal);
        document.querySelector('input[name="government_subsidy"]').addEventListener('input', function() {
            const totalCost = parseFloat(document.querySelector('input[name="total_cost"]').value) || 0;
            updateFinancialCalculations(totalCost);
        });

        // Monthly savings listener
        document.querySelector('input[name="monthly_savings"]').addEventListener('input', function() {
            const totalCost = parseFloat(document.querySelector('input[name="total_cost"]').value) || 0;
            updateFinancialCalculations(totalCost);
        });
    });

    // Form submission
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();
        var form = this;
        var formData = new FormData(form);

        Swal.fire({
            title: 'Creating Quotation...',
            allowOutsideClick: false,
            didOpen: () => { Swal.showLoading(); }
        });

        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(async response => {
            let data;
            try { data = await response.json(); } catch (e) { data = {}; }
            if (response.ok && data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Quotation Created!',
                    text: 'Your comprehensive quotation has been saved successfully.',
                    timer: 2000,
                    showConfirmButton: false,
                    willClose: () => { window.location.href = data.redirect; }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: (data.message || 'An error occurred. Please try again.'),
                    confirmButtonText: 'OK'
                });
            }
        });
    });
    </script>
</div>
@include('components.bottomtab', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
@endsection
