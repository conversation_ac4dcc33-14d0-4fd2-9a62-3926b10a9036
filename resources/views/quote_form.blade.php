@extends('layouts.app')
@section('content')
@include('components.header', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
<div style="max-width:420px;margin:30px auto 70px auto;">
    <form method="POST" action="{{ url('/quote/submit') }}" style="background:#fff;border-radius:16px;box-shadow:0 2px 12px rgba(0,0,0,0.08);padding:1.5rem;">
        @csrf
        <h3 style="font-size:1.15rem;font-weight:600;color:#333;margin-bottom:1.2rem;">Create Quotation</h3>

        <div style="margin-bottom:1.2rem;">
            <label>Select Customer <span style="color:red">*</span></label>
            <select name="customer_id" class="form-control" required>
                <option value="">Choose customer</option>
                @foreach($customers as $cust)
                    <option value="{{ $cust->id }}" {{ $selected_customer == $cust->id ? 'selected' : '' }}>
                        {{ $cust->full_name }} - {{ $cust->mobile }}
                    </option>
                @endforeach
            </select>
        </div>

        @if($customer)
        <div style="background:#f8f9fa;border-radius:8px;padding:1rem;margin-bottom:1.2rem;">
            <div style="font-weight:600;margin-bottom:0.5rem;">Customer Details:</div>
            <div style="font-size:0.9rem;color:#666;">
                <div>📍 {{ $customer->address }}, {{ $customer->city }}</div>
                <div>🔋 Load: {{ $customer->load_requirement }} KW</div>
                <div>🏠 {{ $customer->roof_type }} • {{ $customer->service_type }}</div>
            </div>
        </div>
        @endif
        <div style="margin-bottom:1.2rem;">
            <label>System Capacity (KW) <span style="color:red">*</span></label>
            <input type="number" name="system_capacity" class="form-control" step="0.01" required>
        </div>

        <div style="display:flex;gap:10px;margin-bottom:1.2rem;">
            <div style="flex:1;">
                <label>Panel Count <span style="color:red">*</span></label>
                <input type="number" name="panel_count" class="form-control" required>
            </div>
            <div style="flex:1;">
                <label>Panel Wattage <span style="color:red">*</span></label>
                <input type="number" name="panel_wattage" class="form-control" step="0.01" required>
            </div>
        </div>

        <div style="margin-bottom:1.2rem;">
            <label>Panel Type <span style="color:red">*</span></label>
            <select name="panel_type" class="form-control" required>
                <option value="">Select panel type</option>
                <option value="Monocrystalline">Monocrystalline</option>
                <option value="Polycrystalline">Polycrystalline</option>
                <option value="Bifacial">Bifacial</option>
            </select>
        </div>

        <div style="margin-bottom:1.2rem;">
            <label>Inverter Type <span style="color:red">*</span></label>
            <select name="inverter_type" class="form-control" required>
                <option value="">Select inverter type</option>
                <option value="String Inverter">String Inverter</option>
                <option value="Power Optimizer">Power Optimizer</option>
                <option value="Micro Inverter">Micro Inverter</option>
            </select>
        </div>

        <div style="margin-bottom:1.2rem;">
            <label>Inverter Capacity (KW) <span style="color:red">*</span></label>
            <input type="number" name="inverter_capacity" class="form-control" step="0.01" required>
        </div>

        <div style="margin-bottom:1.2rem;">
            <label>Mounting Structure <span style="color:red">*</span></label>
            <select name="mounting_structure" class="form-control" required>
                <option value="">Select mounting type</option>
                <option value="Roof Mount">Roof Mount</option>
                <option value="Ground Mount">Ground Mount</option>
                <option value="Pole Mount">Pole Mount</option>
            </select>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>System Cost (₹) <span style="color:red">*</span></label>
            <input type="number" name="system_cost" class="form-control" step="0.01" required>
        </div>

        <div style="margin-bottom:1.2rem;">
            <label>Installation Cost (₹) <span style="color:red">*</span></label>
            <input type="number" name="installation_cost" class="form-control" step="0.01" required>
        </div>

        <div style="margin-bottom:1.2rem;">
            <label>Subsidy Amount (₹)</label>
            <input type="number" name="subsidy_amount" class="form-control" step="0.01">
        </div>

        <div style="margin-bottom:1.2rem;">
            <label>Valid Until <span style="color:red">*</span></label>
            <input type="date" name="valid_until" class="form-control" required>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Terms & Conditions</label>
            <textarea name="terms_conditions" class="form-control" rows="3" placeholder="Enter terms and conditions..."></textarea>
        </div>

        <div style="margin-bottom:1.2rem;">
            <label>Notes</label>
            <textarea name="notes" class="form-control" rows="2" placeholder="Additional notes or specifications..."></textarea>
        </div>

        <button type="submit" class="btn btn-warning w-100" style="font-weight:600;font-size:1.08rem;">Create Quotation</button>
    </form>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();
        var form = this;
        var formData = new FormData(form);

        Swal.fire({
            title: 'Creating Quotation...',
            allowOutsideClick: false,
            didOpen: () => { Swal.showLoading(); }
        });

        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(async response => {
            let data;
            try { data = await response.json(); } catch (e) { data = {}; }
            if (response.ok && data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Quotation Created!',
                    text: 'Your quotation has been saved successfully.',
                    timer: 1500,
                    showConfirmButton: false,
                    willClose: () => { window.location.href = data.redirect; }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: (data.message || 'An error occurred. Please try again.'),
                    confirmButtonText: 'OK'
                });
            }
        });
    });
    </script>
</div>
@include('components.bottomtab', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
@endsection
