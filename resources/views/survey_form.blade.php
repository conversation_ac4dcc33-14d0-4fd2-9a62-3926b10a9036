@extends('layouts.app')
@section('content')
@include('components.header', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
<div style="max-width:420px;margin:30px auto 70px auto;">
    <form method="POST" action="{{ url('/survey/submit') }}" enctype="multipart/form-data" style="background:#fff;border-radius:16px;box-shadow:0 2px 12px rgba(0,0,0,0.08);padding:1.5rem;">
        @csrf
    <h3 style="font-size:1.15rem;font-weight:600;color:#333;margin-bottom:1.2rem;">Customer Survey</h3>
        <div style="margin-bottom:1.2rem;">
            <label>Full Name <span style="color:red">*</span></label>
            <input type="text" name="full_name" class="form-control" required>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Mobile Number <span style="color:red">*</span></label>
            <input type="text" name="mobile" class="form-control" required maxlength="10">
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Email</label>
            <input type="email" name="email" class="form-control">
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Age Group</label>
            <select name="age_group" class="form-control">
                <option value="">Select age group</option>
                <option value="18-25">18-25</option>
                <option value="26-35">26-35</option>
                <option value="36-50">36-50</option>
                <option value="51+">51+</option>
            </select>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Address <span style="color:red">*</span></label>
            <input type="text" name="address" class="form-control" required>
        </div>
        <div style="display:flex;gap:10px;margin-bottom:1.2rem;">
            <div style="flex:1;">
                <label>City <span style="color:red">*</span></label>
                <input type="text" name="city" class="form-control" required>
            </div>
            <div style="flex:1;">
                <label>Pincode <span style="color:red">*</span></label>
                <input type="text" name="pincode" class="form-control" required>
            </div>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Service Type <span style="color:red">*</span></label>
            <select name="service_type" class="form-control" required>
                <option value="">Select service</option>
                <option value="Residential">Residential</option>
                <option value="Commercial">Commercial</option>
            </select>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Current Monthly Bill (₹)</label>
            <input type="number" name="monthly_bill" class="form-control">
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Load Requirement (KW) <span style="color:red">*</span></label>
            <select name="load_requirement" class="form-control" required>
                <option value="">Select load</option>
                <option value="1">1 KW</option>
                <option value="2">2 KW</option>
                <option value="3">3 KW</option>
                <option value="5">5 KW</option>
                <option value="10">10 KW</option>
            </select>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Installation Timeline</label>
            <select name="installation_timeline" class="form-control">
                <option value="">Select timeline</option>
                <option value="Immediate">Immediate</option>
                <option value="1 Month">1 Month</option>
                <option value="3 Months">3 Months</option>
                <option value="6 Months">6 Months</option>
            </select>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Roof Type <span style="color:red">*</span></label>
            <select name="roof_type" class="form-control" required>
                <option value="">Select roof type</option>
                <option value="Flat">Flat</option>
                <option value="Sloped">Sloped</option>
                <option value="Other">Other</option>
            </select>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Available Roof Space (Sq. Ft.)</label>
            <input type="number" name="roof_space" class="form-control">
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Shadow Issues</label>
            <select name="shadow_issues" class="form-control">
                <option value="">Any shadow problems?</option>
                <option value="No">No</option>
                <option value="Yes">Yes</option>
            </select>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Property Type</label>
            <select name="property_type" class="form-control">
                <option value="">Select property type</option>
                <option value="Owned">Owned</option>
                <option value="Rented">Rented</option>
            </select>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Capture Location</label>
            <div style="display:flex;gap:10px;">
                <input type="text" name="latitude" class="form-control" placeholder="Latitude">
                <input type="text" name="longitude" class="form-control" placeholder="Longitude">
            </div>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Customer Photo</label>
            <input type="file" name="customer_photo" class="form-control">
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Electricity Bill</label>
            <input type="file" name="bill_photo" class="form-control">
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Roof Photo</label>
            <input type="file" name="roof_photo" class="form-control">
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Property Photo</label>
            <input type="file" name="property_photo" class="form-control">
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Interest <span style="color:red">*</span></label>
            <select name="interest" class="form-control" required>
                <option value="">Select interest</option>
                <option value="Interested">Interested</option>
                <option value="Not Interested">Not Interested</option>
                <option value="May Be Later">May Be Later</option>
            </select>
        </div>
        <div style="margin-bottom:1.2rem;">
            <label>Additional Notes</label>
            <textarea name="notes" class="form-control" rows="2" placeholder="Any special requirements, observations, or notes..."></textarea>
        </div>
        <button type="submit" class="btn btn-warning w-100" style="font-weight:600;font-size:1.08rem;">Submit Survey</button>
    </form>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();
        var form = this;
        var formData = new FormData(form);
        Swal.fire({
            title: 'Submitting...',
            allowOutsideClick: false,
            didOpen: () => { Swal.showLoading(); }
        });
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(async response => {
            let data;
            try { data = await response.json(); } catch (e) { data = {}; }
            if (response.ok && data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Survey Submitted!',
                    text: 'Your survey has been saved.',
                    timer: 1500,
                    showConfirmButton: false,
                    willClose: () => { window.location.href = data.redirect; }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: (data.message || 'An error occurred. Please try again.'),
                    confirmButtonText: 'OK'
                });
            }
        });
    });
    </script>
    </form>
</div>
@include('components.bottomtab', ['usertype' => $usertype, 'Userdetails' => $Userdetails])
@endsection
